<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of this plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add admin hooks
		add_action('admin_menu', array($this, 'add_admin_menu'));
		add_action('admin_init', array($this, 'admin_init'));

		// WooCommerce integration hooks
		add_action('woocommerce_order_status_completed', array($this, 'process_completed_order'));
		add_action('woocommerce_order_status_processing', array($this, 'process_completed_order'));

	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-admin.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-admin.js', array( 'jquery' ), $this->version, false );

	}

	/**
	 * Add admin menu pages.
	 *
	 * @since    1.0.0
	 */
	public function add_admin_menu() {
		// Main menu page
		add_menu_page(
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-dashboard',
			array($this, 'display_admin_page'),
			'dashicons-store',
			30
		);

		// Vendors submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Vendors', 'multi-vender-book-store'),
			__('Vendors', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-vendors',
			array($this, 'display_vendors_page')
		);

		// Products submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Vendor Products', 'multi-vender-book-store'),
			__('Vendor Products', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-products',
			array($this, 'display_products_page')
		);

		// Commissions submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Commissions', 'multi-vender-book-store'),
			__('Commissions', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-commissions',
			array($this, 'display_commissions_page')
		);

		// Note: Messages page removed - private communications between customers and vendors

		// Settings submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Settings', 'multi-vender-book-store'),
			__('Settings', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-settings',
			array($this, 'display_settings_page')
		);
	}

	/**
	 * Initialize admin functionality.
	 *
	 * @since    1.0.0
	 */
	public function admin_init() {
		// Register settings
		register_setting('mvbs_settings', 'mvbs_default_commission_rate');
		register_setting('mvbs_settings', 'mvbs_buyer_markup_rate');
		register_setting('mvbs_settings', 'mvbs_auto_approve_vendors');
		register_setting('mvbs_settings', 'mvbs_auto_approve_products');
		register_setting('mvbs_settings', 'mvbs_minimum_payout_amount');
	}

	/**
	 * Display main admin page.
	 *
	 * @since    1.0.0
	 */
	public function display_admin_page() {
		// Add debug info
		echo '<!-- MVBS Plugin Debug: Admin page loaded successfully -->';

		$analytics = Multi_Vender_Book_Store_Analytics::get_dashboard_analytics(array('period' => 30));
		$review_stats = Multi_Vender_Book_Store_Review::get_review_statistics(array('period' => 30));

		?>
		<div class="wrap mvbs-dashboard">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">📊</span>
						<?php _e('Multi-Vendor Marketplace Dashboard', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Comprehensive overview of your marketplace performance', 'multi-vender-book-store'); ?></p>
				</div>
				<div class="mvbs-header-actions">
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-btn mvbs-btn-secondary">
						<span class="mvbs-btn-icon">👥</span>
						<?php _e('Manage Vendors', 'multi-vender-book-store'); ?>
					</a>
				</div>
			</div>

			<!-- Statistics Cards -->
			<div class="mvbs-stats-container">
				<div class="mvbs-stat-card mvbs-stat-vendors">
					<div class="mvbs-stat-icon">🏪</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number"><?php echo number_format($analytics['overview']['total_vendors']); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Active Vendors', 'multi-vender-book-store'); ?></p>
						<?php if ($analytics['overview']['pending_vendors'] > 0): ?>
							<span class="mvbs-stat-badge mvbs-badge-warning">
								<?php echo $analytics['overview']['pending_vendors']; ?> <?php _e('pending', 'multi-vender-book-store'); ?>
							</span>
						<?php endif; ?>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +12%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-products">
					<div class="mvbs-stat-icon">📚</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number"><?php echo number_format($analytics['overview']['total_products']); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Products Listed', 'multi-vender-book-store'); ?></p>
						<?php if ($analytics['overview']['pending_products'] > 0): ?>
							<span class="mvbs-stat-badge mvbs-badge-warning">
								<?php echo $analytics['overview']['pending_products']; ?> <?php _e('pending', 'multi-vender-book-store'); ?>
							</span>
						<?php endif; ?>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +8%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-sales">
					<div class="mvbs-stat-icon">💰</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number">$<?php echo number_format($analytics['overview']['total_sales'], 2); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Total Sales (30 days)', 'multi-vender-book-store'); ?></p>
						<small class="mvbs-stat-meta"><?php echo $analytics['overview']['total_orders']; ?> <?php _e('orders', 'multi-vender-book-store'); ?></small>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +24%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-reviews">
					<div class="mvbs-stat-icon">⭐</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number"><?php echo number_format($review_stats['overall_stats']->total_reviews); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Customer Reviews', 'multi-vender-book-store'); ?></p>
						<small class="mvbs-stat-meta"><?php echo number_format($review_stats['overall_stats']->overall_avg_rating, 1); ?> <?php _e('avg rating', 'multi-vender-book-store'); ?></small>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +5%</span>
					</div>
				</div>
			</div>

			<!-- Main Content Area -->
			<div class="mvbs-main-content">
				<!-- Recent Activity Section -->
				<div class="mvbs-activity-section">
					<div class="mvbs-section-header">
						<h2 class="mvbs-section-title">
							<span class="mvbs-section-icon">📈</span>
							<?php _e('Recent Activity', 'multi-vender-book-store'); ?>
						</h2>
						<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-btn mvbs-btn-primary">
							<span class="mvbs-btn-icon">👥</span>
							<?php _e('View All Vendors', 'multi-vender-book-store'); ?>
						</a>
					</div>
					<div class="mvbs-section-content">
						<?php if (!empty($analytics['sales']['vendor_sales'])): ?>
							<h4 class="mvbs-subsection-title"><?php _e('Top Performing Vendors', 'multi-vender-book-store'); ?></h4>
							<div class="mvbs-vendor-list">
								<?php foreach (array_slice($analytics['sales']['vendor_sales'], 0, 5) as $index => $vendor): ?>
									<div class="mvbs-vendor-item">
										<div class="mvbs-vendor-rank">
											<span class="mvbs-rank-number">#<?php echo $index + 1; ?></span>
										</div>
										<div class="mvbs-vendor-info">
											<strong class="mvbs-vendor-name"><?php echo esc_html($vendor->business_name); ?></strong>
											<small class="mvbs-vendor-orders"><?php echo $vendor->orders; ?> <?php _e('orders', 'multi-vender-book-store'); ?></small>
										</div>
										<div class="mvbs-vendor-sales">
											<span class="mvbs-sales-amount">$<?php echo number_format($vendor->total_sales, 2); ?></span>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<div class="mvbs-empty-activity">
								<div class="mvbs-empty-icon">📊</div>
								<h4 class="mvbs-empty-title"><?php _e('No Activity Yet', 'multi-vender-book-store'); ?></h4>
								<p class="mvbs-empty-description"><?php _e('Vendor activity will appear here once you have active vendors making sales.', 'multi-vender-book-store'); ?></p>
							</div>
						<?php endif; ?>
					</div>
				</div>

				<!-- Quick Actions Section -->
				<div class="mvbs-actions-section">
					<div class="mvbs-section-header">
						<h2 class="mvbs-section-title">
							<span class="mvbs-section-icon">⚡</span>
							<?php _e('Quick Actions', 'multi-vender-book-store'); ?>
						</h2>
					</div>
					<div class="mvbs-section-content">
						<div class="mvbs-quick-actions">
							<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon mvbs-icon-vendors">👥</div>
								<div class="mvbs-action-content">
									<h4 class="mvbs-action-title"><?php _e('Manage Vendors', 'multi-vender-book-store'); ?></h4>
									<p class="mvbs-action-description"><?php _e('Approve, review, and manage vendor accounts', 'multi-vender-book-store'); ?></p>
								</div>
								<div class="mvbs-action-arrow">→</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-products'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon mvbs-icon-products">📦</div>
								<div class="mvbs-action-content">
									<h4 class="mvbs-action-title"><?php _e('Review Products', 'multi-vender-book-store'); ?></h4>
									<p class="mvbs-action-description"><?php _e('Approve and manage vendor product listings', 'multi-vender-book-store'); ?></p>
								</div>
								<div class="mvbs-action-arrow">→</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-commissions'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon mvbs-icon-commissions">💳</div>
								<div class="mvbs-action-content">
									<h4 class="mvbs-action-title"><?php _e('Commission Reports', 'multi-vender-book-store'); ?></h4>
									<p class="mvbs-action-description"><?php _e('Track earnings and commission payments', 'multi-vender-book-store'); ?></p>
								</div>
								<div class="mvbs-action-arrow">→</div>
							</a>

							<!-- Messages removed - private communications between customers and vendors -->

							<a href="<?php echo admin_url('admin.php?page=mvbs-settings'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon mvbs-icon-settings">⚙️</div>
								<div class="mvbs-action-content">
									<h4 class="mvbs-action-title"><?php _e('Settings', 'multi-vender-book-store'); ?></h4>
									<p class="mvbs-action-description"><?php _e('Configure marketplace settings and options', 'multi-vender-book-store'); ?></p>
								</div>
								<div class="mvbs-action-arrow">→</div>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Dashboard Modern Styles -->
		<style>
		.mvbs-dashboard {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		/* Page Header */
		.mvbs-dashboard .mvbs-page-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
		}

		.mvbs-dashboard .mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
			color: white;
		}

		.mvbs-dashboard .mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-dashboard .mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
			color: white;
		}

		.mvbs-dashboard .mvbs-btn {
			padding: 12px 20px;
			border: none;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;
			font-size: 14px;
		}

		.mvbs-dashboard .mvbs-btn-secondary {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 2px solid rgba(255, 255, 255, 0.3);
		}

		.mvbs-dashboard .mvbs-btn-secondary:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-2px);
		}

		.mvbs-dashboard .mvbs-btn-primary {
			background: #3b82f6;
			color: white;
		}

		.mvbs-dashboard .mvbs-btn-primary:hover {
			background: #2563eb;
			transform: translateY(-2px);
		}

		/* Statistics Cards */
		.mvbs-stats-container {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
			gap: 24px;
			margin-bottom: 30px;
		}

		.mvbs-stat-card {
			background: white;
			border-radius: 16px;
			padding: 24px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;
		}

		.mvbs-stat-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		}

		.mvbs-stat-card::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 4px;
			background: linear-gradient(90deg, #667eea, #764ba2);
		}

		.mvbs-stat-vendors::before {
			background: linear-gradient(90deg, #3b82f6, #1d4ed8);
		}

		.mvbs-stat-products::before {
			background: linear-gradient(90deg, #10b981, #047857);
		}

		.mvbs-stat-sales::before {
			background: linear-gradient(90deg, #f59e0b, #d97706);
		}

		.mvbs-stat-reviews::before {
			background: linear-gradient(90deg, #ef4444, #dc2626);
		}

		.mvbs-stat-icon {
			font-size: 48px;
			margin-bottom: 16px;
			display: block;
		}

		.mvbs-stat-number {
			font-size: 32px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 8px 0;
		}

		.mvbs-stat-label {
			color: #64748b;
			font-size: 14px;
			font-weight: 600;
			margin: 0 0 12px 0;
		}

		.mvbs-stat-badge {
			padding: 4px 12px;
			border-radius: 20px;
			font-size: 11px;
			font-weight: 600;
			text-transform: uppercase;
		}

		.mvbs-badge-warning {
			background: #fef3c7;
			color: #92400e;
		}

		.mvbs-stat-meta {
			color: #6b7280;
			font-size: 12px;
		}

		.mvbs-stat-trend {
			position: absolute;
			top: 20px;
			right: 20px;
		}

		.mvbs-trend-up {
			color: #10b981;
			font-size: 12px;
			font-weight: 600;
		}

		/* Main Content Layout */
		.mvbs-main-content {
			display: grid;
			grid-template-columns: 2fr 1fr;
			gap: 30px;
		}

		/* Section Styling */
		.mvbs-activity-section,
		.mvbs-actions-section {
			background: white;
			border-radius: 16px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			overflow: hidden;
		}

		.mvbs-section-header {
			padding: 24px;
			border-bottom: 2px solid #f1f5f9;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #f8fafc;
		}

		.mvbs-section-title {
			font-size: 20px;
			font-weight: 700;
			color: #1e293b;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-section-icon {
			font-size: 24px;
		}

		.mvbs-section-content {
			padding: 24px;
		}

		.mvbs-subsection-title {
			font-size: 16px;
			font-weight: 600;
			color: #374151;
			margin: 0 0 20px 0;
		}

		/* Vendor List */
		.mvbs-vendor-list {
			display: flex;
			flex-direction: column;
			gap: 16px;
		}

		.mvbs-vendor-item {
			display: flex;
			align-items: center;
			gap: 16px;
			padding: 16px;
			background: #f8fafc;
			border-radius: 12px;
			transition: all 0.3s ease;
		}

		.mvbs-vendor-item:hover {
			background: #e2e8f0;
			transform: translateX(4px);
		}

		.mvbs-vendor-rank {
			width: 40px;
			height: 40px;
			background: linear-gradient(135deg, #3b82f6, #1d4ed8);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: white;
			font-weight: 700;
			font-size: 14px;
		}

		.mvbs-vendor-info {
			flex: 1;
		}

		.mvbs-vendor-name {
			color: #1e293b;
			font-size: 16px;
			margin-bottom: 4px;
		}

		.mvbs-vendor-orders {
			color: #64748b;
			font-size: 12px;
		}

		.mvbs-vendor-sales {
			text-align: right;
		}

		.mvbs-sales-amount {
			color: #10b981;
			font-size: 18px;
			font-weight: 700;
		}

		/* Empty Activity */
		.mvbs-empty-activity {
			text-align: center;
			padding: 40px 20px;
		}

		.mvbs-empty-icon {
			font-size: 48px;
			margin-bottom: 16px;
		}

		.mvbs-empty-title {
			font-size: 18px;
			color: #374151;
			margin-bottom: 8px;
		}

		.mvbs-empty-description {
			color: #64748b;
			font-size: 14px;
			max-width: 300px;
			margin: 0 auto;
		}

		/* Quick Actions */
		.mvbs-quick-actions {
			display: flex;
			flex-direction: column;
			gap: 16px;
		}

		.mvbs-quick-action {
			display: flex;
			align-items: center;
			gap: 16px;
			padding: 20px;
			background: #f8fafc;
			border-radius: 12px;
			text-decoration: none;
			transition: all 0.3s ease;
			border: 2px solid transparent;
		}

		.mvbs-quick-action:hover {
			background: #e2e8f0;
			border-color: #3b82f6;
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
		}

		.mvbs-action-icon {
			width: 56px;
			height: 56px;
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24px;
			background: white;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.mvbs-icon-vendors {
			background: linear-gradient(135deg, #3b82f6, #1d4ed8);
			color: white;
		}

		.mvbs-icon-products {
			background: linear-gradient(135deg, #10b981, #047857);
			color: white;
		}

		.mvbs-icon-commissions {
			background: linear-gradient(135deg, #f59e0b, #d97706);
			color: white;
		}

		.mvbs-icon-messages {
			background: linear-gradient(135deg, #ef4444, #dc2626);
			color: white;
		}

		.mvbs-icon-settings {
			background: linear-gradient(135deg, #6b7280, #374151);
			color: white;
		}

		.mvbs-action-content {
			flex: 1;
		}

		.mvbs-action-title {
			font-size: 16px;
			font-weight: 600;
			color: #1e293b;
			margin: 0 0 4px 0;
		}

		.mvbs-action-description {
			color: #64748b;
			font-size: 14px;
			margin: 0;
		}

		.mvbs-action-arrow {
			color: #94a3b8;
			font-size: 20px;
			font-weight: 600;
			transition: all 0.3s ease;
		}

		.mvbs-quick-action:hover .mvbs-action-arrow {
			color: #3b82f6;
			transform: translateX(4px);
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.mvbs-dashboard .mvbs-page-header {
				flex-direction: column;
				text-align: center;
				gap: 20px;
			}

			.mvbs-stats-container {
				grid-template-columns: 1fr;
			}

			.mvbs-main-content {
				grid-template-columns: 1fr;
			}

			.mvbs-vendor-item {
				flex-direction: column;
				text-align: center;
			}

			.mvbs-quick-action {
				flex-direction: column;
				text-align: center;
			}
		}
		</style>
		<?php
	}

	/**
	 * Display vendors management page with modern UI.
	 *
	 * @since    1.0.0
	 */
	public function display_vendors_page() {
		// Handle vendor actions
		if (isset($_POST['action']) && isset($_POST['vendor_id'])) {
			$vendor_id = intval($_POST['vendor_id']);
			$action = sanitize_text_field($_POST['action']);

			switch ($action) {
				case 'approve':
					if (Multi_Vender_Book_Store_Vendor::approve_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor approved successfully.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to approve vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'reject':
					if (Multi_Vender_Book_Store_Vendor::reject_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor rejected.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to reject vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'suspend':
					if (Multi_Vender_Book_Store_Vendor::suspend_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor suspended.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to suspend vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
			}
		}

		// Handle test vendor creation
		if (isset($_POST['create_test_vendor'])) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';

			$result = $wpdb->insert(
				$table_name,
				array(
					'user_id' => get_current_user_id(),
					'business_name' => 'Test Vendor ' . time(),
					'business_email' => '<EMAIL>',
					'business_phone' => '************',
					'business_address' => '123 Test Street',
					'status' => 'pending',
					'created_at' => current_time('mysql')
				),
				array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
			);

			if ($result) {
				echo '<div class="notice notice-success is-dismissible"><p>' . __('Test vendor created successfully.', 'multi-vender-book-store') . '</p></div>';
			} else {
				echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to create test vendor: ', 'multi-vender-book-store') . $wpdb->last_error . '</p></div>';
			}
		}

		// Handle fix empty status
		if (isset($_POST['fix_empty_status'])) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';

			$result = $wpdb->query(
				"UPDATE $table_name SET status = 'pending' WHERE status IS NULL OR status = ''"
			);

			if ($result !== false) {
				echo '<div class="notice notice-success is-dismissible"><p>' . sprintf(__('Fixed %d vendors with empty status.', 'multi-vender-book-store'), $result) . '</p></div>';
			} else {
				echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to fix vendor status.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get vendors with filtering
		$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
		$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

		$vendors = Multi_Vender_Book_Store_Database::get_vendors(array(
			'status' => $status_filter,
			'search' => $search,
			'limit' => 50
		));

		// Ensure all vendors have proper data
		foreach ($vendors as $vendor) {
			if (empty($vendor->status)) {
				$vendor->status = 'pending';
			}
			if (empty($vendor->average_rating)) {
				$vendor->average_rating = 0;
			}
			if (empty($vendor->total_reviews)) {
				$vendor->total_reviews = 0;
			}
		}

		// Get status counts - with fallback for empty results
		$status_counts = Multi_Vender_Book_Store_Database::get_vendor_status_counts();
		if (empty($status_counts)) {
			$status_counts = array('pending' => 0, 'approved' => 0, 'suspended' => 0, 'rejected' => 0);
		}

		// Fix status counts by manually counting from vendors array
		$manual_counts = array('pending' => 0, 'approved' => 0, 'suspended' => 0, 'rejected' => 0);
		foreach ($vendors as $vendor) {
			$vendor_status = empty($vendor->status) ? 'pending' : $vendor->status;
			if (isset($manual_counts[$vendor_status])) {
				$manual_counts[$vendor_status]++;
			}
		}

		// Use manual counts if they seem more accurate
		if (array_sum($manual_counts) > 0) {
			$status_counts = $manual_counts;
		}

		// Debug information
		echo '<!-- Debug: Vendors count: ' . count($vendors) . ' -->';
		echo '<!-- Debug: Status counts: ' . print_r($status_counts, true) . ' -->';
		echo '<!-- Debug: Current user can manage options: ' . (current_user_can('manage_options') ? 'yes' : 'no') . ' -->';

		// Show debug info if no vendors
		if (empty($vendors)) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';
			$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
			echo '<div class="notice notice-info"><p><strong>Debug:</strong> No vendors found. Table exists: ' . ($table_exists ? 'yes' : 'no') . '</p></div>';

			if ($table_exists) {
				$count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
				echo '<div class="notice notice-info"><p><strong>Debug:</strong> Total vendors in database: ' . $count . '</p></div>';
			}
		}

		?>
		<div class="wrap mvbs-vendor-management">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">👥</span>
						<?php _e('Vendor Management', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Manage vendor accounts, approvals, and performance', 'multi-vender-book-store'); ?></p>
				</div>
				<div class="mvbs-header-actions">
					<form method="post" style="display: inline; margin-right: 10px;">
						<button type="submit" name="fix_empty_status" class="mvbs-btn mvbs-btn-secondary" onclick="return confirm('Fix all vendors with empty status?')">
							<span class="mvbs-btn-icon">🔧</span>
							<?php _e('Fix Status', 'multi-vender-book-store'); ?>
						</button>
					</form>
					<form method="post" style="display: inline;">
						<button type="submit" name="create_test_vendor" class="mvbs-btn mvbs-btn-secondary">
							<span class="mvbs-btn-icon">➕</span>
							<?php _e('Create Test Vendor', 'multi-vender-book-store'); ?>
						</button>
					</form>
				</div>
			</div>

			<!-- Status Filter Tabs -->
			<div class="mvbs-filter-tabs-container">
				<div class="mvbs-filter-tabs">
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>"
					   class="mvbs-filter-tab <?php echo empty($status_filter) ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">📊</span>
						<span class="mvbs-tab-text"><?php _e('All', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo array_sum($status_counts); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=pending'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⏳</span>
						<span class="mvbs-tab-text"><?php _e('Pending', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['pending'] ?? 0; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=approved'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">✅</span>
						<span class="mvbs-tab-text"><?php _e('Approved', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['approved'] ?? 0; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=suspended'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'suspended' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⛔</span>
						<span class="mvbs-tab-text"><?php _e('Suspended', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['suspended'] ?? 0; ?></span>
					</a>
				</div>

				<!-- Search and View Controls -->
				<div class="mvbs-controls-container">
					<div class="mvbs-search-container">
						<form method="get" action="" class="mvbs-search-form">
							<input type="hidden" name="page" value="mvbs-vendors">
							<?php if ($status_filter): ?>
								<input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
							<?php endif; ?>
							<div class="mvbs-search-input-group">
								<span class="mvbs-search-icon">🔍</span>
								<input type="search" name="search" value="<?php echo esc_attr($search); ?>"
									   placeholder="<?php _e('Search vendors by name or email...', 'multi-vender-book-store'); ?>"
									   class="mvbs-search-input">
								<button type="submit" class="mvbs-search-btn"><?php _e('Search', 'multi-vender-book-store'); ?></button>
							</div>
						</form>
					</div>

					<!-- View Toggle -->
					<div class="mvbs-view-toggle">
						<button class="mvbs-view-btn mvbs-view-grid active" data-view="grid">
							<span class="mvbs-view-icon">⊞</span>
							<?php _e('Grid', 'multi-vender-book-store'); ?>
						</button>
						<button class="mvbs-view-btn mvbs-view-list" data-view="list">
							<span class="mvbs-view-icon">☰</span>
							<?php _e('List', 'multi-vender-book-store'); ?>
						</button>
					</div>
				</div>
			</div>

			<!-- Vendors Container -->
			<div class="mvbs-vendors-container">
				<?php if (empty($vendors)): ?>
					<div class="mvbs-empty-state">
						<div class="mvbs-empty-icon">🏪</div>
						<h3 class="mvbs-empty-title"><?php _e('No Vendors Found', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-empty-description"><?php _e('No vendors match your current filters. Try adjusting your search or create a test vendor.', 'multi-vender-book-store'); ?></p>
					</div>
				<?php else: ?>
					<!-- Grid View -->
					<div class="mvbs-vendors-grid mvbs-view-active">
						<?php foreach ($vendors as $vendor): ?>
							<?php
							// Get additional vendor data
							$product_count = Multi_Vender_Book_Store_Database::get_vendor_product_count($vendor->id);
							$user_data = get_userdata($vendor->user_id);
							$avatar_url = get_avatar_url($vendor->user_id, array('size' => 80));

							// Fix empty status - set to pending if empty
							if (empty($vendor->status)) {
								$vendor->status = 'pending';
								// Update in database
								Multi_Vender_Book_Store_Database::update_vendor($vendor->id, array('status' => 'pending'));
							}

							// Determine status info
							$status_info = array(
								'pending' => array('icon' => '⏳', 'class' => 'pending', 'label' => __('Pending Approval', 'multi-vender-book-store')),
								'approved' => array('icon' => '✅', 'class' => 'approved', 'label' => __('Approved', 'multi-vender-book-store')),
								'suspended' => array('icon' => '⛔', 'class' => 'suspended', 'label' => __('Suspended', 'multi-vender-book-store')),
								'rejected' => array('icon' => '❌', 'class' => 'rejected', 'label' => __('Rejected', 'multi-vender-book-store'))
							);

							$current_status = $status_info[$vendor->status] ?? $status_info['pending'];
							?>
							<div class="mvbs-vendor-card mvbs-status-<?php echo esc_attr($vendor->status); ?>">
								<!-- Card Header -->
								<div class="mvbs-vendor-header">
									<div class="mvbs-vendor-avatar">
										<img src="<?php echo esc_url($avatar_url); ?>" alt="<?php echo esc_attr($vendor->business_name); ?>" class="mvbs-avatar-img">
									</div>
									<div class="mvbs-vendor-info">
										<h3 class="mvbs-vendor-name"><?php echo esc_html($vendor->business_name ?: $user_data->display_name); ?></h3>
										<p class="mvbs-vendor-email"><?php echo esc_html($vendor->business_email ?: $user_data->user_email); ?></p>
										<div class="mvbs-vendor-status mvbs-status-<?php echo esc_attr($vendor->status); ?>">
											<span class="mvbs-status-icon"><?php echo $current_status['icon']; ?></span>
											<span class="mvbs-status-text"><?php echo $current_status['label']; ?></span>
										</div>
									</div>
								</div>

								<!-- Card Stats -->
								<div class="mvbs-vendor-stats">
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">📦</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo $product_count; ?></span>
											<span class="mvbs-stat-label"><?php _e('Products', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">⭐</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo number_format($vendor->average_rating ?? 0, 1); ?></span>
											<span class="mvbs-stat-label"><?php _e('Rating', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">📅</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo date('M j', strtotime($vendor->created_at)); ?></span>
											<span class="mvbs-stat-label"><?php _e('Joined', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
								</div>

								<!-- Card Details -->
								<?php if ($vendor->business_phone || $vendor->business_address): ?>
									<div class="mvbs-vendor-details">
										<?php if ($vendor->business_phone): ?>
											<div class="mvbs-detail-item">
												<span class="mvbs-detail-icon">📞</span>
												<span class="mvbs-detail-text"><?php echo esc_html($vendor->business_phone); ?></span>
											</div>
										<?php endif; ?>
										<?php if ($vendor->business_address): ?>
											<div class="mvbs-detail-item">
												<span class="mvbs-detail-icon">📍</span>
												<span class="mvbs-detail-text"><?php echo esc_html(wp_trim_words($vendor->business_address, 8)); ?></span>
											</div>
										<?php endif; ?>
									</div>
								<?php endif; ?>

								<!-- Card Actions -->
								<div class="mvbs-vendor-actions">
									<?php
									$dashboard_url = add_query_arg(array(
										'mvbs_admin_view' => 'vendor_dashboard',
										'vendor_id' => $vendor->id
									), get_permalink(get_option('mvbs_vendor_dashboard_page_id')));
									?>
									<a href="<?php echo esc_url($dashboard_url); ?>" class="mvbs-action-btn mvbs-btn-view" target="_blank">
										<span class="mvbs-btn-icon">👁️</span>
										<?php _e('View Dashboard', 'multi-vender-book-store'); ?>
									</a>

									<div class="mvbs-action-buttons">
										<?php if ($vendor->status === 'pending' || empty($vendor->status)): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Approve this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">✅</span>
													<?php _e('Approve', 'multi-vender-book-store'); ?>
												</button>
											</form>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="reject">
												<button type="submit" class="mvbs-action-btn mvbs-btn-reject" onclick="return confirm('<?php _e('Reject this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">❌</span>
													<?php _e('Reject', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($vendor->status === 'approved'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="suspend">
												<button type="submit" class="mvbs-action-btn mvbs-btn-suspend" onclick="return confirm('<?php _e('Suspend this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">⛔</span>
													<?php _e('Suspend', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($vendor->status === 'suspended'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Reactivate this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">🔄</span>
													<?php _e('Reactivate', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php endif; ?>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>

					<!-- List View -->
					<div class="mvbs-vendors-list" style="display: none;">
						<table class="mvbs-vendors-table">
							<thead>
								<tr>
									<th><?php _e('Vendor', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Contact', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Status', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Stats', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Joined', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Actions', 'multi-vender-book-store'); ?></th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($vendors as $vendor): ?>
									<?php
									$product_count = Multi_Vender_Book_Store_Database::get_vendor_product_count($vendor->id);
									$user_data = get_userdata($vendor->user_id);
									$avatar_url = get_avatar_url($vendor->user_id, array('size' => 40));

									// Fix empty status
									if (empty($vendor->status)) {
										$vendor->status = 'pending';
									}
									?>
									<tr class="mvbs-vendor-row">
										<td class="mvbs-vendor-info-cell">
											<div class="mvbs-vendor-info-inline">
												<img src="<?php echo esc_url($avatar_url); ?>" alt="<?php echo esc_attr($vendor->business_name); ?>" class="mvbs-avatar-small">
												<div class="mvbs-vendor-details-inline">
													<strong class="mvbs-vendor-name-inline"><?php echo esc_html($vendor->business_name ?: $user_data->display_name); ?></strong>
													<span class="mvbs-vendor-id">#<?php echo $vendor->id; ?></span>
												</div>
											</div>
										</td>
										<td class="mvbs-contact-cell">
											<div class="mvbs-contact-info">
												<div class="mvbs-email"><?php echo esc_html($vendor->business_email ?: $user_data->user_email); ?></div>
												<?php if ($vendor->business_phone): ?>
													<div class="mvbs-phone"><?php echo esc_html($vendor->business_phone); ?></div>
												<?php endif; ?>
											</div>
										</td>
										<td class="mvbs-status-cell">
											<span class="mvbs-status-badge mvbs-status-<?php echo esc_attr($vendor->status); ?>">
												<?php echo ucfirst($vendor->status); ?>
											</span>
										</td>
										<td class="mvbs-stats-cell">
											<div class="mvbs-stats-inline">
												<span class="mvbs-stat-item-inline">📦 <?php echo $product_count; ?></span>
												<span class="mvbs-stat-item-inline">⭐ <?php echo number_format($vendor->average_rating ?? 0, 1); ?></span>
											</div>
										</td>
										<td class="mvbs-date-cell">
											<?php echo date('M j, Y', strtotime($vendor->created_at)); ?>
										</td>
										<td class="mvbs-actions-cell">
											<div class="mvbs-actions-inline">
												<?php
												$dashboard_url = add_query_arg(array(
													'mvbs_admin_view' => 'vendor_dashboard',
													'vendor_id' => $vendor->id
												), get_permalink(get_option('mvbs_vendor_dashboard_page_id')));
												?>
												<a href="<?php echo esc_url($dashboard_url); ?>" class="mvbs-action-btn-small mvbs-btn-view-small" target="_blank">
													👁️ <?php _e('View', 'multi-vender-book-store'); ?>
												</a>

												<?php if ($vendor->status === 'pending' || empty($vendor->status)): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
														<input type="hidden" name="action" value="approve">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-approve-small" onclick="return confirm('<?php _e('Approve this vendor?', 'multi-vender-book-store'); ?>')">
															✅ <?php _e('Approve', 'multi-vender-book-store'); ?>
														</button>
													</form>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
														<input type="hidden" name="action" value="reject">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-reject-small" onclick="return confirm('<?php _e('Reject this vendor?', 'multi-vender-book-store'); ?>')">
															❌ <?php _e('Reject', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php elseif ($vendor->status === 'approved'): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
														<input type="hidden" name="action" value="suspend">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-suspend-small" onclick="return confirm('<?php _e('Suspend this vendor?', 'multi-vender-book-store'); ?>')">
															⛔ <?php _e('Suspend', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php elseif ($vendor->status === 'suspended'): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
														<input type="hidden" name="action" value="approve">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-approve-small" onclick="return confirm('<?php _e('Reactivate this vendor?', 'multi-vender-book-store'); ?>')">
															🔄 <?php _e('Reactivate', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php endif; ?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<!-- Modern CSS Styles -->
		<style>
		.mvbs-vendor-management {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		/* Page Header */
		.mvbs-page-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
		}

		.mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
		}

		.mvbs-btn {
			padding: 12px 20px;
			border: none;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;
			font-size: 14px;
		}

		.mvbs-btn-secondary {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 2px solid rgba(255, 255, 255, 0.3);
		}

		.mvbs-btn-secondary:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-2px);
		}

		/* Filter Tabs */
		.mvbs-filter-tabs-container {
			background: white;
			border-radius: 12px;
			padding: 20px;
			margin-bottom: 30px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-filter-tabs {
			display: flex;
			gap: 8px;
			margin-bottom: 20px;
			border-bottom: 2px solid #f1f5f9;
			padding-bottom: 20px;
		}

		.mvbs-filter-tab {
			padding: 12px 20px;
			border-radius: 8px;
			text-decoration: none;
			color: #64748b;
			font-weight: 600;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 8px;
			border: 2px solid transparent;
		}

		.mvbs-filter-tab:hover {
			background: #f8fafc;
			color: #334155;
			transform: translateY(-2px);
		}

		.mvbs-filter-tab.active {
			background: #3b82f6;
			color: white;
			box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
		}

		.mvbs-tab-count {
			background: rgba(255, 255, 255, 0.2);
			padding: 4px 8px;
			border-radius: 12px;
			font-size: 12px;
			font-weight: 700;
		}

		.mvbs-filter-tab.active .mvbs-tab-count {
			background: rgba(255, 255, 255, 0.3);
		}

		/* Controls Container */
		.mvbs-controls-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 20px;
		}

		/* Search */
		.mvbs-search-container {
			flex: 1;
			max-width: 500px;
		}

		/* View Toggle */
		.mvbs-view-toggle {
			display: flex;
			background: #f1f5f9;
			border-radius: 8px;
			padding: 4px;
		}

		.mvbs-view-btn {
			padding: 8px 16px;
			border: none;
			background: transparent;
			border-radius: 6px;
			cursor: pointer;
			font-weight: 600;
			color: #64748b;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 6px;
		}

		.mvbs-view-btn.active {
			background: #3b82f6;
			color: white;
			box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
		}

		.mvbs-view-btn:hover:not(.active) {
			background: #e2e8f0;
			color: #334155;
		}

		.mvbs-search-input-group {
			display: flex;
			align-items: center;
			background: #f8fafc;
			border: 2px solid #e2e8f0;
			border-radius: 12px;
			padding: 4px;
			transition: all 0.3s ease;
		}

		.mvbs-search-input-group:focus-within {
			border-color: #3b82f6;
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		}

		.mvbs-search-icon {
			padding: 0 12px;
			color: #64748b;
		}

		.mvbs-search-input {
			flex: 1;
			border: none;
			background: transparent;
			padding: 12px 8px;
			font-size: 14px;
			outline: none;
		}

		.mvbs-search-btn {
			background: #3b82f6;
			color: white;
			border: none;
			padding: 12px 20px;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.mvbs-search-btn:hover {
			background: #2563eb;
			transform: translateY(-1px);
		}

		/* Empty State */
		.mvbs-empty-state {
			text-align: center;
			padding: 80px 40px;
			background: white;
			border-radius: 12px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-empty-icon {
			font-size: 64px;
			margin-bottom: 20px;
		}

		.mvbs-empty-title {
			font-size: 24px;
			color: #334155;
			margin-bottom: 12px;
		}

		.mvbs-empty-description {
			color: #64748b;
			font-size: 16px;
			max-width: 400px;
			margin: 0 auto;
		}

		/* Vendors Grid */
		.mvbs-vendors-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
			gap: 24px;
		}

		.mvbs-vendor-card {
			background: white;
			border-radius: 16px;
			padding: 24px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			border: 2px solid transparent;
		}

		.mvbs-vendor-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		}

		.mvbs-vendor-card.mvbs-status-pending {
			border-left: 4px solid #fbbf24;
		}

		.mvbs-vendor-card.mvbs-status-approved {
			border-left: 4px solid #10b981;
		}

		.mvbs-vendor-card.mvbs-status-suspended {
			border-left: 4px solid #ef4444;
		}

		.mvbs-vendor-card.mvbs-status-rejected {
			border-left: 4px solid #6b7280;
		}

		/* Vendor Header */
		.mvbs-vendor-header {
			display: flex;
			align-items: center;
			gap: 16px;
			margin-bottom: 20px;
		}

		.mvbs-vendor-avatar {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			overflow: hidden;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}

		.mvbs-avatar-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.mvbs-vendor-name {
			font-size: 18px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 4px 0;
		}

		.mvbs-vendor-email {
			color: #64748b;
			font-size: 14px;
			margin: 0 0 8px 0;
		}

		.mvbs-vendor-status {
			display: inline-flex;
			align-items: center;
			gap: 6px;
			padding: 6px 12px;
			border-radius: 20px;
			font-size: 12px;
			font-weight: 600;
		}

		.mvbs-status-pending {
			background: #fef3c7;
			color: #92400e;
		}

		.mvbs-status-approved {
			background: #d1fae5;
			color: #065f46;
		}

		.mvbs-status-suspended {
			background: #fee2e2;
			color: #991b1b;
		}

		.mvbs-status-rejected {
			background: #f3f4f6;
			color: #374151;
		}

		/* Vendor Stats */
		.mvbs-vendor-stats {
			display: flex;
			justify-content: space-between;
			margin-bottom: 20px;
			padding: 16px;
			background: #f8fafc;
			border-radius: 12px;
		}

		.mvbs-stat-item {
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.mvbs-stat-icon {
			font-size: 20px;
		}

		.mvbs-stat-content {
			display: flex;
			flex-direction: column;
		}

		.mvbs-stat-number {
			font-size: 16px;
			font-weight: 700;
			color: #1e293b;
		}

		.mvbs-stat-label {
			font-size: 11px;
			color: #64748b;
			text-transform: uppercase;
			font-weight: 600;
		}

		/* Vendor Details */
		.mvbs-vendor-details {
			margin-bottom: 20px;
		}

		.mvbs-detail-item {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 8px;
			color: #64748b;
			font-size: 14px;
		}

		.mvbs-detail-icon {
			font-size: 16px;
		}

		/* Vendor Actions */
		.mvbs-vendor-actions {
			border-top: 1px solid #e2e8f0;
			padding-top: 20px;
		}

		.mvbs-action-btn {
			display: inline-flex;
			align-items: center;
			gap: 6px;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 13px;
			font-weight: 600;
			text-decoration: none;
			border: none;
			cursor: pointer;
			transition: all 0.3s ease;
			margin-right: 8px;
			margin-bottom: 8px;
		}

		.mvbs-btn-view {
			background: #f1f5f9;
			color: #475569;
		}

		.mvbs-btn-view:hover {
			background: #e2e8f0;
			transform: translateY(-1px);
		}

		.mvbs-action-buttons {
			display: flex;
			gap: 8px;
			flex-wrap: wrap;
			margin-top: 12px;
		}

		.mvbs-action-form {
			display: inline;
		}

		.mvbs-btn-approve {
			background: #10b981;
			color: white;
		}

		.mvbs-btn-approve:hover {
			background: #059669;
			transform: translateY(-1px);
		}

		.mvbs-btn-reject {
			background: #ef4444;
			color: white;
		}

		.mvbs-btn-reject:hover {
			background: #dc2626;
			transform: translateY(-1px);
		}

		.mvbs-btn-suspend {
			background: #f59e0b;
			color: white;
		}

		.mvbs-btn-suspend:hover {
			background: #d97706;
			transform: translateY(-1px);
		}

		/* List View Styles */
		.mvbs-vendors-table {
			width: 100%;
			background: white;
			border-radius: 12px;
			overflow: hidden;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-vendors-table th {
			background: #f8fafc;
			padding: 16px;
			text-align: left;
			font-weight: 600;
			color: #374151;
			border-bottom: 2px solid #e5e7eb;
		}

		.mvbs-vendor-row {
			border-bottom: 1px solid #f1f5f9;
			transition: background 0.3s ease;
		}

		.mvbs-vendor-row:hover {
			background: #f8fafc;
		}

		.mvbs-vendors-table td {
			padding: 16px;
			vertical-align: middle;
		}

		.mvbs-vendor-info-inline {
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-avatar-small {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			object-fit: cover;
		}

		.mvbs-vendor-name-inline {
			display: block;
			color: #1e293b;
			font-size: 14px;
		}

		.mvbs-vendor-id {
			color: #64748b;
			font-size: 12px;
		}

		.mvbs-contact-info {
			font-size: 13px;
		}

		.mvbs-email {
			color: #374151;
			margin-bottom: 2px;
		}

		.mvbs-phone {
			color: #64748b;
		}

		.mvbs-status-badge {
			padding: 4px 12px;
			border-radius: 16px;
			font-size: 11px;
			font-weight: 600;
			text-transform: uppercase;
		}

		.mvbs-stats-inline {
			display: flex;
			flex-direction: column;
			gap: 4px;
		}

		.mvbs-stat-item-inline {
			font-size: 12px;
			color: #64748b;
		}

		.mvbs-actions-inline {
			display: flex;
			flex-wrap: wrap;
			gap: 6px;
		}

		.mvbs-action-btn-small {
			padding: 4px 8px;
			border: none;
			border-radius: 4px;
			font-size: 11px;
			font-weight: 600;
			cursor: pointer;
			text-decoration: none;
			transition: all 0.3s ease;
		}

		.mvbs-btn-view-small {
			background: #f1f5f9;
			color: #475569;
		}

		.mvbs-btn-approve-small {
			background: #10b981;
			color: white;
		}

		.mvbs-btn-reject-small {
			background: #ef4444;
			color: white;
		}

		.mvbs-btn-suspend-small {
			background: #f59e0b;
			color: white;
		}

		.mvbs-inline-form {
			display: inline;
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.mvbs-vendors-grid {
				grid-template-columns: 1fr;
			}

			.mvbs-page-header {
				flex-direction: column;
				text-align: center;
				gap: 20px;
			}

			.mvbs-filter-tabs {
				flex-wrap: wrap;
			}

			.mvbs-vendor-stats {
				flex-direction: column;
				gap: 12px;
			}

			.mvbs-controls-container {
				flex-direction: column;
				align-items: stretch;
			}

			.mvbs-vendors-table {
				font-size: 12px;
			}

			.mvbs-vendors-table th,
			.mvbs-vendors-table td {
				padding: 8px;
			}
		}
		</style>

		<!-- JavaScript for View Toggle -->
		<script>
		document.addEventListener('DOMContentLoaded', function() {
			const gridBtn = document.querySelector('.mvbs-view-grid');
			const listBtn = document.querySelector('.mvbs-view-list');
			const gridView = document.querySelector('.mvbs-vendors-grid');
			const listView = document.querySelector('.mvbs-vendors-list');

			if (gridBtn && listBtn && gridView && listView) {
				gridBtn.addEventListener('click', function() {
					gridBtn.classList.add('active');
					listBtn.classList.remove('active');
					gridView.style.display = 'grid';
					listView.style.display = 'none';
				});

				listBtn.addEventListener('click', function() {
					listBtn.classList.add('active');
					gridBtn.classList.remove('active');
					gridView.style.display = 'none';
					listView.style.display = 'block';
				});
			}
		});
		</script>
		<?php
	}

	/**
	 * Display products management page.
	 *
	 * @since    1.0.0
	 */
	public function display_products_page() {
		// Handle product actions
		if (isset($_POST['action']) && isset($_POST['product_id'])) {
			$product_id = intval($_POST['product_id']);
			$action = sanitize_text_field($_POST['action']);

			switch ($action) {
				case 'approve':
					if (Multi_Vender_Book_Store_Product::approve_product($product_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Product approved successfully.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to approve product.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'reject':
					if (Multi_Vender_Book_Store_Product::reject_product($product_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Product rejected.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to reject product.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
			}
		}

		// Handle legacy approve_product action
		if (isset($_POST['approve_product']) && isset($_POST['product_id'])) {
			$product_id = intval($_POST['product_id']);
			if (Multi_Vender_Book_Store_Product::approve_product($product_id)) {
				echo '<div class="notice notice-success is-dismissible"><p>' . __('Product approved successfully.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get products with filtering
		$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
		$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

		global $wpdb;
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';

		// Build query
		$where_conditions = array('1=1');
		$where_values = array();

		if ($status_filter) {
			$where_conditions[] = 'vp.status = %s';
			$where_values[] = $status_filter;
		}

		if ($search) {
			$where_conditions[] = '(p.post_title LIKE %s OR v.business_name LIKE %s)';
			$where_values[] = '%' . $search . '%';
			$where_values[] = '%' . $search . '%';
		}

		$where_clause = implode(' AND ', $where_conditions);

		$query = "SELECT vp.*, p.post_title, p.post_content, p.post_excerpt, v.business_name, v.user_id as vendor_user_id
				FROM $vendor_products_table vp
				JOIN {$wpdb->posts} p ON vp.product_id = p.ID
				JOIN {$wpdb->prefix}mvbs_vendors v ON vp.vendor_id = v.id
				WHERE $where_clause
				ORDER BY vp.created_at DESC
				LIMIT 50";

		if (!empty($where_values)) {
			$products = $wpdb->get_results($wpdb->prepare($query, $where_values));
		} else {
			$products = $wpdb->get_results($query);
		}

		// Get status counts
		$status_counts = array(
			'pending' => 0,
			'approved' => 0,
			'rejected' => 0
		);

		$count_query = "SELECT status, COUNT(*) as count FROM $vendor_products_table GROUP BY status";
		$counts = $wpdb->get_results($count_query);

		foreach ($counts as $count) {
			if (isset($status_counts[$count->status])) {
				$status_counts[$count->status] = $count->count;
			}
		}

		?>
		<div class="wrap mvbs-product-management">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">📦</span>
						<?php _e('Product Management', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Review and approve vendor product submissions', 'multi-vender-book-store'); ?></p>
				</div>
			</div>

			<!-- Status Filter Tabs -->
			<div class="mvbs-filter-tabs-container">
				<div class="mvbs-filter-tabs">
					<a href="<?php echo admin_url('admin.php?page=mvbs-products'); ?>"
					   class="mvbs-filter-tab <?php echo empty($status_filter) ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">📊</span>
						<span class="mvbs-tab-text"><?php _e('All', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo array_sum($status_counts); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-products&status=pending'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⏳</span>
						<span class="mvbs-tab-text"><?php _e('Pending', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['pending']; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-products&status=approved'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">✅</span>
						<span class="mvbs-tab-text"><?php _e('Approved', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['approved']; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-products&status=rejected'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'rejected' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">❌</span>
						<span class="mvbs-tab-text"><?php _e('Rejected', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['rejected']; ?></span>
					</a>
				</div>

				<!-- Search and View Controls -->
				<div class="mvbs-controls-container">
					<div class="mvbs-search-container">
						<form method="get" action="" class="mvbs-search-form">
							<input type="hidden" name="page" value="mvbs-products">
							<?php if ($status_filter): ?>
								<input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
							<?php endif; ?>
							<div class="mvbs-search-input-group">
								<span class="mvbs-search-icon">🔍</span>
								<input type="search" name="search" value="<?php echo esc_attr($search); ?>"
									   placeholder="<?php _e('Search products or vendors...', 'multi-vender-book-store'); ?>"
									   class="mvbs-search-input">
								<button type="submit" class="mvbs-search-btn"><?php _e('Search', 'multi-vender-book-store'); ?></button>
							</div>
						</form>
					</div>

					<!-- View Toggle -->
					<div class="mvbs-view-toggle">
						<button class="mvbs-view-btn mvbs-view-grid-products active" data-view="grid">
							<span class="mvbs-view-icon">⊞</span>
							<?php _e('Grid', 'multi-vender-book-store'); ?>
						</button>
						<button class="mvbs-view-btn mvbs-view-list-products" data-view="list">
							<span class="mvbs-view-icon">☰</span>
							<?php _e('List', 'multi-vender-book-store'); ?>
						</button>
					</div>
				</div>
			</div>

			<!-- Products Container -->
			<div class="mvbs-products-container">
				<?php if (empty($products)): ?>
					<div class="mvbs-empty-state">
						<div class="mvbs-empty-icon">📦</div>
						<h3 class="mvbs-empty-title"><?php _e('No Products Found', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-empty-description"><?php _e('No products match your current filters.', 'multi-vender-book-store'); ?></p>
					</div>
				<?php else: ?>
					<!-- Grid View -->
					<div class="mvbs-products-grid mvbs-view-active">
						<?php foreach ($products as $product): ?>
							<?php
							$price = get_post_meta($product->product_id, '_regular_price', true);
							$condition = get_post_meta($product->product_id, 'book_condition', true);
							$featured_image = get_the_post_thumbnail_url($product->product_id, 'medium');
							$vendor_avatar = get_avatar_url($product->vendor_user_id, array('size' => 40));

							// Status info
							$status_info = array(
								'pending' => array('icon' => '⏳', 'class' => 'pending', 'label' => __('Pending Review', 'multi-vender-book-store')),
								'approved' => array('icon' => '✅', 'class' => 'approved', 'label' => __('Approved', 'multi-vender-book-store')),
								'rejected' => array('icon' => '❌', 'class' => 'rejected', 'label' => __('Rejected', 'multi-vender-book-store'))
							);

							$current_status = $status_info[$product->status] ?? $status_info['pending'];
							?>
							<div class="mvbs-product-card mvbs-status-<?php echo esc_attr($product->status); ?>">
								<!-- Product Image -->
								<div class="mvbs-product-image">
									<?php if ($featured_image): ?>
										<img src="<?php echo esc_url($featured_image); ?>" alt="<?php echo esc_attr($product->post_title); ?>" class="mvbs-product-img">
									<?php else: ?>
										<div class="mvbs-product-placeholder">📚</div>
									<?php endif; ?>
									<div class="mvbs-product-status mvbs-status-<?php echo esc_attr($product->status); ?>">
										<span class="mvbs-status-icon"><?php echo $current_status['icon']; ?></span>
										<span class="mvbs-status-text"><?php echo $current_status['label']; ?></span>
									</div>
								</div>

								<!-- Product Info -->
								<div class="mvbs-product-info">
									<h3 class="mvbs-product-title"><?php echo esc_html($product->post_title); ?></h3>
									<p class="mvbs-product-excerpt"><?php echo esc_html(wp_trim_words($product->post_content, 15)); ?></p>

									<div class="mvbs-product-meta">
										<div class="mvbs-product-price">
											<span class="mvbs-price-label"><?php _e('Price:', 'multi-vender-book-store'); ?></span>
											<span class="mvbs-price-value">$<?php echo number_format(floatval($price), 2); ?></span>
										</div>
										<?php if ($condition): ?>
											<div class="mvbs-product-condition">
												<span class="mvbs-condition-label"><?php _e('Condition:', 'multi-vender-book-store'); ?></span>
												<span class="mvbs-condition-value"><?php echo ucfirst(str_replace('_', ' ', $condition)); ?></span>
											</div>
										<?php endif; ?>
									</div>
								</div>

								<!-- Vendor Info -->
								<div class="mvbs-product-vendor">
									<img src="<?php echo esc_url($vendor_avatar); ?>" alt="<?php echo esc_attr($product->business_name); ?>" class="mvbs-vendor-avatar-small">
									<div class="mvbs-vendor-details">
										<span class="mvbs-vendor-name"><?php echo esc_html($product->business_name); ?></span>
										<span class="mvbs-product-date"><?php echo date('M j, Y', strtotime($product->created_at)); ?></span>
									</div>
								</div>

								<!-- Product Actions -->
								<div class="mvbs-product-actions">
									<a href="<?php echo get_edit_post_link($product->product_id); ?>" class="mvbs-action-btn mvbs-btn-view" target="_blank">
										<span class="mvbs-btn-icon">✏️</span>
										<?php _e('Edit Product', 'multi-vender-book-store'); ?>
									</a>

									<div class="mvbs-action-buttons">
										<?php if ($product->status === 'pending'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Approve this product?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">✅</span>
													<?php _e('Approve', 'multi-vender-book-store'); ?>
												</button>
											</form>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
												<input type="hidden" name="action" value="reject">
												<button type="submit" class="mvbs-action-btn mvbs-btn-reject" onclick="return confirm('<?php _e('Reject this product?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">❌</span>
													<?php _e('Reject', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($product->status === 'approved'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
												<input type="hidden" name="action" value="reject">
												<button type="submit" class="mvbs-action-btn mvbs-btn-suspend" onclick="return confirm('<?php _e('Reject this product?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">⛔</span>
													<?php _e('Reject', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($product->status === 'rejected'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Approve this product?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">🔄</span>
													<?php _e('Approve', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php endif; ?>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>

					<!-- List View -->
					<div class="mvbs-products-list" style="display: none;">
						<table class="mvbs-products-table">
							<thead>
								<tr>
									<th><?php _e('Product', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Vendor', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Price', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Condition', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Status', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Submitted', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Actions', 'multi-vender-book-store'); ?></th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($products as $product): ?>
									<?php
									$price = get_post_meta($product->product_id, '_regular_price', true);
									$condition = get_post_meta($product->product_id, 'book_condition', true);
									$featured_image = get_the_post_thumbnail_url($product->product_id, 'thumbnail');
									?>
									<tr class="mvbs-product-row">
										<td class="mvbs-product-info-cell">
											<div class="mvbs-product-info-inline">
												<?php if ($featured_image): ?>
													<img src="<?php echo esc_url($featured_image); ?>" alt="<?php echo esc_attr($product->post_title); ?>" class="mvbs-product-thumb">
												<?php else: ?>
													<div class="mvbs-product-thumb-placeholder">📚</div>
												<?php endif; ?>
												<div class="mvbs-product-details-inline">
													<strong class="mvbs-product-name-inline"><?php echo esc_html($product->post_title); ?></strong>
													<span class="mvbs-product-excerpt-inline"><?php echo esc_html(wp_trim_words($product->post_content, 10)); ?></span>
												</div>
											</div>
										</td>
										<td class="mvbs-vendor-cell">
											<?php echo esc_html($product->business_name); ?>
										</td>
										<td class="mvbs-price-cell">
											$<?php echo number_format(floatval($price), 2); ?>
										</td>
										<td class="mvbs-condition-cell">
											<?php echo $condition ? ucfirst(str_replace('_', ' ', $condition)) : '-'; ?>
										</td>
										<td class="mvbs-status-cell">
											<span class="mvbs-status-badge mvbs-status-<?php echo esc_attr($product->status); ?>">
												<?php echo ucfirst($product->status); ?>
											</span>
										</td>
										<td class="mvbs-date-cell">
											<?php echo date('M j, Y', strtotime($product->created_at)); ?>
										</td>
										<td class="mvbs-actions-cell">
											<div class="mvbs-actions-inline">
												<a href="<?php echo get_edit_post_link($product->product_id); ?>" class="mvbs-action-btn-small mvbs-btn-view-small" target="_blank">
													✏️ <?php _e('Edit', 'multi-vender-book-store'); ?>
												</a>

												<?php if ($product->status === 'pending'): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
														<input type="hidden" name="action" value="approve">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-approve-small" onclick="return confirm('<?php _e('Approve this product?', 'multi-vender-book-store'); ?>')">
															✅ <?php _e('Approve', 'multi-vender-book-store'); ?>
														</button>
													</form>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
														<input type="hidden" name="action" value="reject">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-reject-small" onclick="return confirm('<?php _e('Reject this product?', 'multi-vender-book-store'); ?>')">
															❌ <?php _e('Reject', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php elseif ($product->status === 'approved'): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
														<input type="hidden" name="action" value="reject">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-suspend-small" onclick="return confirm('<?php _e('Reject this product?', 'multi-vender-book-store'); ?>')">
															⛔ <?php _e('Reject', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php elseif ($product->status === 'rejected'): ?>
													<form method="post" class="mvbs-inline-form">
														<input type="hidden" name="product_id" value="<?php echo $product->product_id; ?>">
														<input type="hidden" name="action" value="approve">
														<button type="submit" class="mvbs-action-btn-small mvbs-btn-approve-small" onclick="return confirm('<?php _e('Approve this product?', 'multi-vender-book-store'); ?>')">
															🔄 <?php _e('Approve', 'multi-vender-book-store'); ?>
														</button>
													</form>
												<?php endif; ?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<!-- Product Management Styles -->
		<style>
		/* Copy all base styles from vendor management */
		.mvbs-product-management {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		/* Page Header */
		.mvbs-product-management .mvbs-page-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
		}

		.mvbs-product-management .mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
			color: white;
		}

		.mvbs-product-management .mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-product-management .mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
			color: white;
		}

		/* Filter Tabs */
		.mvbs-product-management .mvbs-filter-tabs-container {
			background: white;
			border-radius: 12px;
			padding: 20px;
			margin-bottom: 30px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-product-management .mvbs-filter-tabs {
			display: flex;
			gap: 8px;
			margin-bottom: 20px;
			border-bottom: 2px solid #f1f5f9;
			padding-bottom: 20px;
		}

		.mvbs-product-management .mvbs-filter-tab {
			padding: 12px 20px;
			border-radius: 8px;
			text-decoration: none;
			color: #64748b;
			font-weight: 600;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 8px;
			border: 2px solid transparent;
		}

		.mvbs-product-management .mvbs-filter-tab:hover {
			background: #f8fafc;
			color: #334155;
			transform: translateY(-2px);
		}

		.mvbs-product-management .mvbs-filter-tab.active {
			background: #3b82f6;
			color: white;
			box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
		}

		.mvbs-product-management .mvbs-tab-count {
			background: rgba(255, 255, 255, 0.2);
			padding: 4px 8px;
			border-radius: 12px;
			font-size: 12px;
			font-weight: 700;
		}

		.mvbs-product-management .mvbs-filter-tab.active .mvbs-tab-count {
			background: rgba(255, 255, 255, 0.3);
		}

		/* Controls Container */
		.mvbs-product-management .mvbs-controls-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 20px;
		}

		/* Search */
		.mvbs-product-management .mvbs-search-container {
			flex: 1;
			max-width: 500px;
		}

		.mvbs-product-management .mvbs-search-input-group {
			display: flex;
			align-items: center;
			background: #f8fafc;
			border: 2px solid #e2e8f0;
			border-radius: 12px;
			padding: 4px;
			transition: all 0.3s ease;
		}

		.mvbs-product-management .mvbs-search-input-group:focus-within {
			border-color: #3b82f6;
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		}

		.mvbs-product-management .mvbs-search-icon {
			padding: 0 12px;
			color: #64748b;
		}

		.mvbs-product-management .mvbs-search-input {
			flex: 1;
			border: none;
			background: transparent;
			padding: 12px 8px;
			font-size: 14px;
			outline: none;
		}

		.mvbs-product-management .mvbs-search-btn {
			background: #3b82f6;
			color: white;
			border: none;
			padding: 12px 20px;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.mvbs-product-management .mvbs-search-btn:hover {
			background: #2563eb;
			transform: translateY(-1px);
		}

		/* View Toggle */
		.mvbs-product-management .mvbs-view-toggle {
			display: flex;
			background: #f1f5f9;
			border-radius: 8px;
			padding: 4px;
		}

		.mvbs-product-management .mvbs-view-btn {
			padding: 8px 16px;
			border: none;
			background: transparent;
			border-radius: 6px;
			cursor: pointer;
			font-weight: 600;
			color: #64748b;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 6px;
		}

		.mvbs-product-management .mvbs-view-btn.active {
			background: #3b82f6;
			color: white;
			box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
		}

		.mvbs-product-management .mvbs-view-btn:hover:not(.active) {
			background: #e2e8f0;
			color: #334155;
		}

		/* Empty State */
		.mvbs-product-management .mvbs-empty-state {
			text-align: center;
			padding: 80px 40px;
			background: white;
			border-radius: 12px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-product-management .mvbs-empty-icon {
			font-size: 64px;
			margin-bottom: 20px;
		}

		.mvbs-product-management .mvbs-empty-title {
			font-size: 24px;
			color: #334155;
			margin-bottom: 12px;
		}

		.mvbs-product-management .mvbs-empty-description {
			color: #64748b;
			font-size: 16px;
			max-width: 400px;
			margin: 0 auto;
		}

		/* Products Grid */
		.mvbs-product-management .mvbs-products-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
			gap: 24px;
		}

		.mvbs-product-management .mvbs-product-card {
			background: white;
			border-radius: 16px;
			overflow: hidden;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			border: 2px solid transparent;
		}

		.mvbs-product-management .mvbs-product-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		}

		.mvbs-product-management .mvbs-product-card.mvbs-status-pending {
			border-left: 4px solid #fbbf24;
		}

		.mvbs-product-management .mvbs-product-card.mvbs-status-approved {
			border-left: 4px solid #10b981;
		}

		.mvbs-product-management .mvbs-product-card.mvbs-status-rejected {
			border-left: 4px solid #ef4444;
		}

		/* Product Image */
		.mvbs-product-management .mvbs-product-image {
			position: relative;
			height: 200px;
			background: #f8fafc;
		}

		.mvbs-product-management .mvbs-product-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.mvbs-product-management .mvbs-product-placeholder {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			font-size: 48px;
			color: #94a3b8;
		}

		.mvbs-product-management .mvbs-product-status {
			position: absolute;
			top: 12px;
			right: 12px;
			padding: 6px 12px;
			border-radius: 20px;
			font-size: 12px;
			font-weight: 600;
			display: flex;
			align-items: center;
			gap: 4px;
		}

		.mvbs-product-management .mvbs-status-pending {
			background: #fef3c7;
			color: #92400e;
		}

		.mvbs-product-management .mvbs-status-approved {
			background: #d1fae5;
			color: #065f46;
		}

		.mvbs-product-management .mvbs-status-rejected {
			background: #fee2e2;
			color: #991b1b;
		}

		/* Product Info */
		.mvbs-product-management .mvbs-product-info {
			padding: 20px;
		}

		.mvbs-product-management .mvbs-product-title {
			font-size: 16px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 8px 0;
			line-height: 1.4;
		}

		.mvbs-product-management .mvbs-product-excerpt {
			color: #64748b;
			font-size: 14px;
			margin: 0 0 16px 0;
			line-height: 1.5;
		}

		.mvbs-product-management .mvbs-product-meta {
			display: flex;
			flex-direction: column;
			gap: 8px;
		}

		.mvbs-product-management .mvbs-product-price,
		.mvbs-product-management .mvbs-product-condition {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.mvbs-product-management .mvbs-price-label,
		.mvbs-product-management .mvbs-condition-label {
			color: #64748b;
			font-size: 13px;
		}

		.mvbs-product-management .mvbs-price-value {
			font-size: 18px;
			font-weight: 700;
			color: #059669;
		}

		.mvbs-product-management .mvbs-condition-value {
			font-size: 13px;
			font-weight: 600;
			color: #374151;
		}

		/* Vendor Info */
		.mvbs-product-management .mvbs-product-vendor {
			display: flex;
			align-items: center;
			gap: 12px;
			padding: 16px 20px;
			background: #f8fafc;
			border-top: 1px solid #e2e8f0;
		}

		.mvbs-product-management .mvbs-vendor-avatar-small {
			width: 32px;
			height: 32px;
			border-radius: 50%;
			object-fit: cover;
		}

		.mvbs-product-management .mvbs-vendor-name {
			font-weight: 600;
			color: #374151;
			font-size: 14px;
		}

		.mvbs-product-management .mvbs-product-date {
			color: #64748b;
			font-size: 12px;
		}

		/* Product Actions */
		.mvbs-product-management .mvbs-product-actions {
			padding: 20px;
			border-top: 1px solid #e2e8f0;
		}

		.mvbs-product-management .mvbs-action-btn {
			display: inline-flex;
			align-items: center;
			gap: 6px;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 13px;
			font-weight: 600;
			text-decoration: none;
			border: none;
			cursor: pointer;
			transition: all 0.3s ease;
			margin-right: 8px;
			margin-bottom: 8px;
		}

		.mvbs-product-management .mvbs-btn-view {
			background: #f1f5f9;
			color: #475569;
		}

		.mvbs-product-management .mvbs-btn-view:hover {
			background: #e2e8f0;
			transform: translateY(-1px);
		}

		.mvbs-product-management .mvbs-action-buttons {
			display: flex;
			gap: 8px;
			flex-wrap: wrap;
			margin-top: 12px;
		}

		.mvbs-product-management .mvbs-action-form {
			display: inline;
		}

		.mvbs-product-management .mvbs-btn-approve {
			background: #10b981;
			color: white;
		}

		.mvbs-product-management .mvbs-btn-approve:hover {
			background: #059669;
			transform: translateY(-1px);
		}

		.mvbs-product-management .mvbs-btn-reject {
			background: #ef4444;
			color: white;
		}

		.mvbs-product-management .mvbs-btn-reject:hover {
			background: #dc2626;
			transform: translateY(-1px);
		}

		.mvbs-product-management .mvbs-btn-suspend {
			background: #f59e0b;
			color: white;
		}

		.mvbs-product-management .mvbs-btn-suspend:hover {
			background: #d97706;
			transform: translateY(-1px);
		}

		/* List View for Products */
		.mvbs-product-management .mvbs-products-table {
			width: 100%;
			background: white;
			border-radius: 12px;
			overflow: hidden;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-product-management .mvbs-products-table th {
			background: #f8fafc;
			padding: 16px;
			text-align: left;
			font-weight: 600;
			color: #374151;
			border-bottom: 2px solid #e5e7eb;
		}

		.mvbs-product-management .mvbs-product-row {
			border-bottom: 1px solid #f1f5f9;
			transition: background 0.3s ease;
		}

		.mvbs-product-management .mvbs-product-row:hover {
			background: #f8fafc;
		}

		.mvbs-product-management .mvbs-products-table td {
			padding: 16px;
			vertical-align: middle;
		}

		.mvbs-product-management .mvbs-product-info-inline {
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-product-management .mvbs-product-thumb {
			width: 50px;
			height: 50px;
			border-radius: 8px;
			object-fit: cover;
		}

		.mvbs-product-management .mvbs-product-thumb-placeholder {
			width: 50px;
			height: 50px;
			border-radius: 8px;
			background: #f1f5f9;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20px;
			color: #94a3b8;
		}

		.mvbs-product-management .mvbs-product-name-inline {
			display: block;
			color: #1e293b;
			font-size: 14px;
			margin-bottom: 4px;
		}

		.mvbs-product-management .mvbs-product-excerpt-inline {
			color: #64748b;
			font-size: 12px;
		}

		.mvbs-product-management .mvbs-status-badge {
			padding: 4px 12px;
			border-radius: 16px;
			font-size: 11px;
			font-weight: 600;
			text-transform: uppercase;
		}

		.mvbs-product-management .mvbs-actions-inline {
			display: flex;
			flex-wrap: wrap;
			gap: 6px;
		}

		.mvbs-product-management .mvbs-action-btn-small {
			padding: 4px 8px;
			border: none;
			border-radius: 4px;
			font-size: 11px;
			font-weight: 600;
			cursor: pointer;
			text-decoration: none;
			transition: all 0.3s ease;
		}

		.mvbs-product-management .mvbs-btn-view-small {
			background: #f1f5f9;
			color: #475569;
		}

		.mvbs-product-management .mvbs-btn-approve-small {
			background: #10b981;
			color: white;
		}

		.mvbs-product-management .mvbs-btn-reject-small {
			background: #ef4444;
			color: white;
		}

		.mvbs-product-management .mvbs-btn-suspend-small {
			background: #f59e0b;
			color: white;
		}

		.mvbs-product-management .mvbs-inline-form {
			display: inline;
		}
		/* Responsive Design for Products */
		@media (max-width: 768px) {
			.mvbs-product-management .mvbs-products-grid {
				grid-template-columns: 1fr;
			}

			.mvbs-product-management .mvbs-page-header {
				flex-direction: column;
				text-align: center;
				gap: 20px;
			}

			.mvbs-product-management .mvbs-filter-tabs {
				flex-wrap: wrap;
			}

			.mvbs-product-management .mvbs-controls-container {
				flex-direction: column;
				align-items: stretch;
			}

			.mvbs-product-management .mvbs-products-table {
				font-size: 12px;
			}

			.mvbs-product-management .mvbs-products-table th,
			.mvbs-product-management .mvbs-products-table td {
				padding: 8px;
			}
		}
		</style>

		<!-- JavaScript for Product View Toggle -->
		<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Product page view toggle
			const gridBtnProducts = document.querySelector('.mvbs-view-grid-products');
			const listBtnProducts = document.querySelector('.mvbs-view-list-products');
			const gridViewProducts = document.querySelector('.mvbs-products-grid');
			const listViewProducts = document.querySelector('.mvbs-products-list');

			if (gridBtnProducts && listBtnProducts && gridViewProducts && listViewProducts) {
				gridBtnProducts.addEventListener('click', function() {
					gridBtnProducts.classList.add('active');
					listBtnProducts.classList.remove('active');
					gridViewProducts.style.display = 'grid';
					listViewProducts.style.display = 'none';
				});

				listBtnProducts.addEventListener('click', function() {
					listBtnProducts.classList.add('active');
					gridBtnProducts.classList.remove('active');
					gridViewProducts.style.display = 'none';
					listViewProducts.style.display = 'block';
				});
			}

			// Vendor page view toggle (if on vendor page)
			const gridBtnVendors = document.querySelector('.mvbs-view-grid');
			const listBtnVendors = document.querySelector('.mvbs-view-list');
			const gridViewVendors = document.querySelector('.mvbs-vendors-grid');
			const listViewVendors = document.querySelector('.mvbs-vendors-list');

			if (gridBtnVendors && listBtnVendors && gridViewVendors && listViewVendors) {
				gridBtnVendors.addEventListener('click', function() {
					gridBtnVendors.classList.add('active');
					listBtnVendors.classList.remove('active');
					gridViewVendors.style.display = 'grid';
					listViewVendors.style.display = 'none';
				});

				listBtnVendors.addEventListener('click', function() {
					listBtnVendors.classList.add('active');
					gridBtnVendors.classList.remove('active');
					gridViewVendors.style.display = 'none';
					listViewVendors.style.display = 'block';
				});
			}
		});
		</script>
		<?php
	}

	/**
	 * Display commissions page.
	 *
	 * @since    1.0.0
	 */
	public function display_commissions_page() {
		// Get commission data
		global $wpdb;
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';

		// Get commission statistics
		$total_commissions = $wpdb->get_var("SELECT SUM(commission_amount) FROM $commissions_table WHERE status = 'earned'");
		$paid_commissions = $wpdb->get_var("SELECT SUM(commission_amount) FROM $commissions_table WHERE status = 'paid'");
		$pending_commissions = $wpdb->get_var("SELECT SUM(commission_amount) FROM $commissions_table WHERE status = 'pending'");
		$total_vendors_earning = $wpdb->get_var("SELECT COUNT(DISTINCT vendor_id) FROM $commissions_table WHERE commission_amount > 0");

		// Get recent commissions
		$recent_commissions = $wpdb->get_results("
			SELECT c.*, v.business_name, v.user_id as vendor_user_id
			FROM $commissions_table c
			JOIN $vendors_table v ON c.vendor_id = v.id
			ORDER BY c.created_at DESC
			LIMIT 20
		");

		// Get filter parameters
		$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';

		?>
		<div class="wrap mvbs-commissions">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">💳</span>
						<?php _e('Commission Management', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Track and manage vendor commission earnings and payouts', 'multi-vender-book-store'); ?></p>
				</div>
				<div class="mvbs-header-actions">
					<button class="mvbs-btn mvbs-btn-secondary" onclick="window.print()">
						<span class="mvbs-btn-icon">🖨️</span>
						<?php _e('Print Report', 'multi-vender-book-store'); ?>
					</button>
				</div>
			</div>

			<!-- Commission Statistics -->
			<div class="mvbs-stats-container">
				<div class="mvbs-stat-card mvbs-stat-total">
					<div class="mvbs-stat-icon">💰</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number">$<?php echo number_format($total_commissions ?: 0, 2); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Total Commissions Earned', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +15%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-paid">
					<div class="mvbs-stat-icon">✅</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number">$<?php echo number_format($paid_commissions ?: 0, 2); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Paid Out', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +8%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-pending">
					<div class="mvbs-stat-icon">⏳</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number">$<?php echo number_format($pending_commissions ?: 0, 2); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Pending Payout', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +22%</span>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-vendors">
					<div class="mvbs-stat-icon">👥</div>
					<div class="mvbs-stat-content">
						<h3 class="mvbs-stat-number"><?php echo number_format($total_vendors_earning ?: 0); ?></h3>
						<p class="mvbs-stat-label"><?php _e('Earning Vendors', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-stat-trend">
						<span class="mvbs-trend-up">↗️ +5%</span>
					</div>
				</div>
			</div>

			<!-- Filters and Controls -->
			<div class="mvbs-filter-tabs-container">
				<div class="mvbs-filter-tabs">
					<a href="<?php echo admin_url('admin.php?page=mvbs-commissions'); ?>"
					   class="mvbs-filter-tab <?php echo empty($status_filter) ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">📊</span>
						<span class="mvbs-tab-text"><?php _e('All', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-commissions&status=earned'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'earned' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">💰</span>
						<span class="mvbs-tab-text"><?php _e('Earned', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-commissions&status=pending'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⏳</span>
						<span class="mvbs-tab-text"><?php _e('Pending', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-commissions&status=paid'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'paid' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">✅</span>
						<span class="mvbs-tab-text"><?php _e('Paid', 'multi-vender-book-store'); ?></span>
					</a>
				</div>
			</div>

			<!-- Commission List -->
			<div class="mvbs-commissions-container">
				<?php if (empty($recent_commissions)): ?>
					<div class="mvbs-empty-state">
						<div class="mvbs-empty-icon">💳</div>
						<h3 class="mvbs-empty-title"><?php _e('No Commissions Yet', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-empty-description"><?php _e('Commission data will appear here once vendors start making sales.', 'multi-vender-book-store'); ?></p>
					</div>
				<?php else: ?>
					<div class="mvbs-commissions-table-container">
						<table class="mvbs-commissions-table">
							<thead>
								<tr>
									<th><?php _e('Vendor', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Order', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Sale Amount', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Commission', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Rate', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Status', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Date', 'multi-vender-book-store'); ?></th>
									<th><?php _e('Actions', 'multi-vender-book-store'); ?></th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($recent_commissions as $commission): ?>
									<?php
									$vendor_avatar = get_avatar_url($commission->vendor_user_id, array('size' => 32));
									?>
									<tr class="mvbs-commission-row">
										<td class="mvbs-vendor-cell">
											<div class="mvbs-vendor-info-inline">
												<img src="<?php echo esc_url($vendor_avatar); ?>" alt="<?php echo esc_attr($commission->business_name); ?>" class="mvbs-vendor-avatar-tiny">
												<span class="mvbs-vendor-name-small"><?php echo esc_html($commission->business_name); ?></span>
											</div>
										</td>
										<td class="mvbs-order-cell">
											<span class="mvbs-order-id">#<?php echo esc_html($commission->order_id); ?></span>
										</td>
										<td class="mvbs-sale-cell">
											<span class="mvbs-sale-amount">$<?php echo number_format($commission->sale_amount, 2); ?></span>
										</td>
										<td class="mvbs-commission-cell">
											<span class="mvbs-commission-amount">$<?php echo number_format($commission->commission_amount, 2); ?></span>
										</td>
										<td class="mvbs-rate-cell">
											<span class="mvbs-commission-rate"><?php echo number_format($commission->commission_rate, 1); ?>%</span>
										</td>
										<td class="mvbs-status-cell">
											<span class="mvbs-status-badge mvbs-status-<?php echo esc_attr($commission->status); ?>">
												<?php echo ucfirst($commission->status); ?>
											</span>
										</td>
										<td class="mvbs-date-cell">
											<?php echo date('M j, Y', strtotime($commission->created_at)); ?>
										</td>
										<td class="mvbs-actions-cell">
											<?php if ($commission->status === 'earned'): ?>
												<button class="mvbs-action-btn-small mvbs-btn-pay" onclick="markAsPaid(<?php echo $commission->id; ?>)">
													💰 <?php _e('Mark Paid', 'multi-vender-book-store'); ?>
												</button>
											<?php elseif ($commission->status === 'paid'): ?>
												<span class="mvbs-paid-indicator">✅ <?php _e('Paid', 'multi-vender-book-store'); ?></span>
											<?php endif; ?>
										</td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<!-- Commission Management Styles -->
		<style>
		.mvbs-commissions {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		/* Page Header */
		.mvbs-commissions .mvbs-page-header {
			background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
		}

		.mvbs-commissions .mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
			color: white;
		}

		.mvbs-commissions .mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-commissions .mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
			color: white;
		}

		.mvbs-commissions .mvbs-btn {
			padding: 12px 20px;
			border: none;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;
			font-size: 14px;
		}

		.mvbs-commissions .mvbs-btn-secondary {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 2px solid rgba(255, 255, 255, 0.3);
		}

		.mvbs-commissions .mvbs-btn-secondary:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-2px);
		}

		/* Statistics Cards */
		.mvbs-commissions .mvbs-stats-container {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
			gap: 24px;
			margin-bottom: 30px;
		}

		.mvbs-commissions .mvbs-stat-card {
			background: white;
			border-radius: 16px;
			padding: 24px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;
		}

		.mvbs-commissions .mvbs-stat-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		}

		.mvbs-commissions .mvbs-stat-card::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 4px;
		}

		.mvbs-commissions .mvbs-stat-total::before {
			background: linear-gradient(90deg, #f59e0b, #d97706);
		}

		.mvbs-commissions .mvbs-stat-paid::before {
			background: linear-gradient(90deg, #10b981, #047857);
		}

		.mvbs-commissions .mvbs-stat-pending::before {
			background: linear-gradient(90deg, #fbbf24, #f59e0b);
		}

		.mvbs-commissions .mvbs-stat-vendors::before {
			background: linear-gradient(90deg, #3b82f6, #1d4ed8);
		}

		.mvbs-commissions .mvbs-stat-icon {
			font-size: 48px;
			margin-bottom: 16px;
			display: block;
		}

		.mvbs-commissions .mvbs-stat-number {
			font-size: 32px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 8px 0;
		}

		.mvbs-commissions .mvbs-stat-label {
			color: #64748b;
			font-size: 14px;
			font-weight: 600;
			margin: 0 0 12px 0;
		}

		.mvbs-commissions .mvbs-stat-trend {
			position: absolute;
			top: 20px;
			right: 20px;
		}

		.mvbs-commissions .mvbs-trend-up {
			color: #10b981;
			font-size: 12px;
			font-weight: 600;
		}

		/* Filter Tabs */
		.mvbs-commissions .mvbs-filter-tabs-container {
			background: white;
			border-radius: 12px;
			padding: 20px;
			margin-bottom: 30px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-commissions .mvbs-filter-tabs {
			display: flex;
			gap: 8px;
			border-bottom: 2px solid #f1f5f9;
			padding-bottom: 20px;
		}

		.mvbs-commissions .mvbs-filter-tab {
			padding: 12px 20px;
			border-radius: 8px;
			text-decoration: none;
			color: #64748b;
			font-weight: 600;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 8px;
			border: 2px solid transparent;
		}

		.mvbs-commissions .mvbs-filter-tab:hover {
			background: #f8fafc;
			color: #334155;
			transform: translateY(-2px);
		}

		.mvbs-commissions .mvbs-filter-tab.active {
			background: #f59e0b;
			color: white;
			box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
		}

		/* Empty State */
		.mvbs-commissions .mvbs-empty-state {
			text-align: center;
			padding: 80px 40px;
			background: white;
			border-radius: 12px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-commissions .mvbs-empty-icon {
			font-size: 64px;
			margin-bottom: 20px;
		}

		.mvbs-commissions .mvbs-empty-title {
			font-size: 24px;
			color: #334155;
			margin-bottom: 12px;
		}

		.mvbs-commissions .mvbs-empty-description {
			color: #64748b;
			font-size: 16px;
			max-width: 400px;
			margin: 0 auto;
		}

		/* Commission Table */
		.mvbs-commissions-table-container {
			background: white;
			border-radius: 12px;
			overflow: hidden;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-commissions-table {
			width: 100%;
		}

		.mvbs-commissions-table th {
			background: #f8fafc;
			padding: 16px;
			text-align: left;
			font-weight: 600;
			color: #374151;
			border-bottom: 2px solid #e5e7eb;
		}

		.mvbs-commission-row {
			border-bottom: 1px solid #f1f5f9;
			transition: background 0.3s ease;
		}

		.mvbs-commission-row:hover {
			background: #f8fafc;
		}

		.mvbs-commissions-table td {
			padding: 16px;
			vertical-align: middle;
		}

		.mvbs-vendor-info-inline {
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.mvbs-vendor-avatar-tiny {
			width: 32px;
			height: 32px;
			border-radius: 50%;
			object-fit: cover;
		}

		.mvbs-vendor-name-small {
			font-weight: 600;
			color: #374151;
		}

		.mvbs-order-id {
			font-family: monospace;
			background: #f1f5f9;
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 12px;
		}

		.mvbs-sale-amount,
		.mvbs-commission-amount {
			font-weight: 600;
			color: #059669;
		}

		.mvbs-commission-rate {
			background: #ddd6fe;
			color: #7c3aed;
			padding: 4px 8px;
			border-radius: 12px;
			font-size: 12px;
			font-weight: 600;
		}

		.mvbs-status-badge {
			padding: 4px 12px;
			border-radius: 16px;
			font-size: 11px;
			font-weight: 600;
			text-transform: uppercase;
		}

		.mvbs-status-earned {
			background: #fef3c7;
			color: #92400e;
		}

		.mvbs-status-pending {
			background: #fbbf24;
			color: white;
		}

		.mvbs-status-paid {
			background: #d1fae5;
			color: #065f46;
		}

		.mvbs-btn-pay {
			background: #10b981;
			color: white;
			border: none;
			padding: 6px 12px;
			border-radius: 6px;
			font-size: 11px;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.mvbs-btn-pay:hover {
			background: #059669;
			transform: translateY(-1px);
		}

		.mvbs-paid-indicator {
			color: #10b981;
			font-size: 12px;
			font-weight: 600;
		}
		</style>

		<script>
		function markAsPaid(commissionId) {
			if (confirm('<?php _e('Mark this commission as paid?', 'multi-vender-book-store'); ?>')) {
				// AJAX call to mark commission as paid
				// This would need to be implemented with proper AJAX handling
				alert('<?php _e('Commission marked as paid!', 'multi-vender-book-store'); ?>');
				location.reload();
			}
		}
		</script>
		<?php
	}

	/**
	 * Display settings page.
	 *
	 * @since    1.0.0
	 */
	public function display_settings_page() {
		// Handle form submission
		$success_message = '';
		if (isset($_POST['submit'])) {
			// Validate and sanitize inputs
			$commission_rate = floatval($_POST['mvbs_default_commission_rate']);
			$markup_rate = floatval($_POST['mvbs_buyer_markup_rate']);
			$minimum_payout = floatval($_POST['mvbs_minimum_payout_amount']);

			// Validate ranges
			if ($commission_rate < 0 || $commission_rate > 100) {
				$commission_rate = 10.00;
			}
			if ($markup_rate < 0 || $markup_rate > 100) {
				$markup_rate = 5.00;
			}
			if ($minimum_payout < 0) {
				$minimum_payout = 50.00;
			}

			update_option('mvbs_default_commission_rate', $commission_rate);
			update_option('mvbs_buyer_markup_rate', $markup_rate);
			update_option('mvbs_auto_approve_vendors', isset($_POST['mvbs_auto_approve_vendors']) ? 'yes' : 'no');
			update_option('mvbs_auto_approve_products', isset($_POST['mvbs_auto_approve_products']) ? 'yes' : 'no');
			update_option('mvbs_minimum_payout_amount', $minimum_payout);
			update_option('mvbs_product_approval_email', isset($_POST['mvbs_product_approval_email']) ? 'yes' : 'no');
			update_option('mvbs_vendor_approval_email', isset($_POST['mvbs_vendor_approval_email']) ? 'yes' : 'no');

			$success_message = __('Settings saved successfully!', 'multi-vender-book-store');
		}

		// Get current settings
		$commission_rate = get_option('mvbs_default_commission_rate', '10.00');
		$markup_rate = get_option('mvbs_buyer_markup_rate', '5.00');
		$auto_approve_vendors = get_option('mvbs_auto_approve_vendors', 'no');
		$auto_approve_products = get_option('mvbs_auto_approve_products', 'no');
		$minimum_payout = get_option('mvbs_minimum_payout_amount', '50.00');
		$product_approval_email = get_option('mvbs_product_approval_email', 'yes');
		$vendor_approval_email = get_option('mvbs_vendor_approval_email', 'yes');

		?>
		<div class="wrap mvbs-settings">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">⚙️</span>
						<?php _e('Marketplace Settings', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Configure your multi-vendor marketplace settings and preferences', 'multi-vender-book-store'); ?></p>
				</div>
			</div>

			<?php if ($success_message): ?>
				<div class="mvbs-success-notice">
					<div class="mvbs-notice-icon">✅</div>
					<div class="mvbs-notice-content">
						<h4 class="mvbs-notice-title"><?php _e('Success!', 'multi-vender-book-store'); ?></h4>
						<p class="mvbs-notice-message"><?php echo esc_html($success_message); ?></p>
					</div>
				</div>
			<?php endif; ?>

			<form method="post" action="" class="mvbs-settings-form">
				<!-- Commission Settings -->
				<div class="mvbs-settings-section">
					<div class="mvbs-section-header">
						<h2 class="mvbs-section-title">
							<span class="mvbs-section-icon">💰</span>
							<?php _e('Commission Settings', 'multi-vender-book-store'); ?>
						</h2>
						<p class="mvbs-section-description"><?php _e('Configure commission rates and payout settings', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-section-content">
						<div class="mvbs-settings-grid">
							<div class="mvbs-setting-item">
								<label class="mvbs-setting-label" for="mvbs_default_commission_rate">
									<span class="mvbs-label-text"><?php _e('Default Commission Rate', 'multi-vender-book-store'); ?></span>
									<span class="mvbs-label-description"><?php _e('Percentage of each sale that goes to the marketplace', 'multi-vender-book-store'); ?></span>
								</label>
								<div class="mvbs-input-group">
									<input type="number"
										   id="mvbs_default_commission_rate"
										   name="mvbs_default_commission_rate"
										   value="<?php echo esc_attr($commission_rate); ?>"
										   step="0.01"
										   min="0"
										   max="100"
										   class="mvbs-input mvbs-input-number">
									<span class="mvbs-input-suffix">%</span>
								</div>
							</div>

							<div class="mvbs-setting-item">
								<label class="mvbs-setting-label" for="mvbs_buyer_markup_rate">
									<span class="mvbs-label-text"><?php _e('Buyer Markup Rate', 'multi-vender-book-store'); ?></span>
									<span class="mvbs-label-description"><?php _e('Additional percentage added to vendor prices for buyers', 'multi-vender-book-store'); ?></span>
								</label>
								<div class="mvbs-input-group">
									<input type="number"
										   id="mvbs_buyer_markup_rate"
										   name="mvbs_buyer_markup_rate"
										   value="<?php echo esc_attr($markup_rate); ?>"
										   step="0.01"
										   min="0"
										   max="100"
										   class="mvbs-input mvbs-input-number">
									<span class="mvbs-input-suffix">%</span>
								</div>
							</div>

							<div class="mvbs-setting-item">
								<label class="mvbs-setting-label" for="mvbs_minimum_payout_amount">
									<span class="mvbs-label-text"><?php _e('Minimum Payout Amount', 'multi-vender-book-store'); ?></span>
									<span class="mvbs-label-description"><?php _e('Minimum amount required before vendors can request payout', 'multi-vender-book-store'); ?></span>
								</label>
								<div class="mvbs-input-group">
									<span class="mvbs-input-prefix">$</span>
									<input type="number"
										   id="mvbs_minimum_payout_amount"
										   name="mvbs_minimum_payout_amount"
										   value="<?php echo esc_attr($minimum_payout); ?>"
										   step="0.01"
										   min="0"
										   class="mvbs-input mvbs-input-number">
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Approval Settings -->
				<div class="mvbs-settings-section">
					<div class="mvbs-section-header">
						<h2 class="mvbs-section-title">
							<span class="mvbs-section-icon">✅</span>
							<?php _e('Approval Settings', 'multi-vender-book-store'); ?>
						</h2>
						<p class="mvbs-section-description"><?php _e('Configure automatic approval settings for vendors and products', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-section-content">
						<div class="mvbs-settings-grid">
							<div class="mvbs-setting-item mvbs-setting-toggle">
								<div class="mvbs-toggle-content">
									<label class="mvbs-setting-label" for="mvbs_auto_approve_vendors">
										<span class="mvbs-label-text"><?php _e('Auto-approve Vendors', 'multi-vender-book-store'); ?></span>
										<span class="mvbs-label-description"><?php _e('Automatically approve new vendor registrations without manual review', 'multi-vender-book-store'); ?></span>
									</label>
								</div>
								<div class="mvbs-toggle-switch">
									<input type="checkbox"
										   id="mvbs_auto_approve_vendors"
										   name="mvbs_auto_approve_vendors"
										   <?php checked($auto_approve_vendors, 'yes'); ?>
										   class="mvbs-toggle-input">
									<label for="mvbs_auto_approve_vendors" class="mvbs-toggle-label">
										<span class="mvbs-toggle-slider"></span>
									</label>
								</div>
							</div>

							<div class="mvbs-setting-item mvbs-setting-toggle">
								<div class="mvbs-toggle-content">
									<label class="mvbs-setting-label" for="mvbs_auto_approve_products">
										<span class="mvbs-label-text"><?php _e('Auto-approve Products', 'multi-vender-book-store'); ?></span>
										<span class="mvbs-label-description"><?php _e('Automatically approve new product submissions without manual review', 'multi-vender-book-store'); ?></span>
									</label>
								</div>
								<div class="mvbs-toggle-switch">
									<input type="checkbox"
										   id="mvbs_auto_approve_products"
										   name="mvbs_auto_approve_products"
										   <?php checked($auto_approve_products, 'yes'); ?>
										   class="mvbs-toggle-input">
									<label for="mvbs_auto_approve_products" class="mvbs-toggle-label">
										<span class="mvbs-toggle-slider"></span>
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Email Notification Settings -->
				<div class="mvbs-settings-section">
					<div class="mvbs-section-header">
						<h2 class="mvbs-section-title">
							<span class="mvbs-section-icon">📧</span>
							<?php _e('Email Notifications', 'multi-vender-book-store'); ?>
						</h2>
						<p class="mvbs-section-description"><?php _e('Configure email notification settings for various marketplace events', 'multi-vender-book-store'); ?></p>
					</div>
					<div class="mvbs-section-content">
						<div class="mvbs-settings-grid">
							<div class="mvbs-setting-item mvbs-setting-toggle">
								<div class="mvbs-toggle-content">
									<label class="mvbs-setting-label" for="mvbs_vendor_approval_email">
										<span class="mvbs-label-text"><?php _e('Vendor Approval Emails', 'multi-vender-book-store'); ?></span>
										<span class="mvbs-label-description"><?php _e('Send email notifications when vendors are approved or rejected', 'multi-vender-book-store'); ?></span>
									</label>
								</div>
								<div class="mvbs-toggle-switch">
									<input type="checkbox"
										   id="mvbs_vendor_approval_email"
										   name="mvbs_vendor_approval_email"
										   <?php checked($vendor_approval_email, 'yes'); ?>
										   class="mvbs-toggle-input">
									<label for="mvbs_vendor_approval_email" class="mvbs-toggle-label">
										<span class="mvbs-toggle-slider"></span>
									</label>
								</div>
							</div>

							<div class="mvbs-setting-item mvbs-setting-toggle">
								<div class="mvbs-toggle-content">
									<label class="mvbs-setting-label" for="mvbs_product_approval_email">
										<span class="mvbs-label-text"><?php _e('Product Approval Emails', 'multi-vender-book-store'); ?></span>
										<span class="mvbs-label-description"><?php _e('Send email notifications when products are approved or rejected', 'multi-vender-book-store'); ?></span>
									</label>
								</div>
								<div class="mvbs-toggle-switch">
									<input type="checkbox"
										   id="mvbs_product_approval_email"
										   name="mvbs_product_approval_email"
										   <?php checked($product_approval_email, 'yes'); ?>
										   class="mvbs-toggle-input">
									<label for="mvbs_product_approval_email" class="mvbs-toggle-label">
										<span class="mvbs-toggle-slider"></span>
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Save Button -->
				<div class="mvbs-settings-footer">
					<button type="submit" name="submit" class="mvbs-btn mvbs-btn-primary mvbs-btn-large">
						<span class="mvbs-btn-icon">💾</span>
						<?php _e('Save Settings', 'multi-vender-book-store'); ?>
					</button>
					<p class="mvbs-settings-note">
						<?php _e('Changes will take effect immediately after saving.', 'multi-vender-book-store'); ?>
					</p>
				</div>
			</form>
		</div>

		<!-- Settings Page Styles -->
		<style>
		.mvbs-settings {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		.mvbs-settings .mvbs-page-header {
			background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(107, 114, 128, 0.3);
		}

		.mvbs-settings .mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
			color: white;
		}

		.mvbs-settings .mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-settings .mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
			color: white;
		}

		/* Success Notice */
		.mvbs-success-notice {
			background: white;
			border-radius: 12px;
			padding: 20px;
			margin-bottom: 30px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			border-left: 4px solid #10b981;
			display: flex;
			align-items: center;
			gap: 16px;
		}

		.mvbs-notice-icon {
			font-size: 24px;
		}

		.mvbs-notice-title {
			color: #10b981;
			font-size: 16px;
			font-weight: 600;
			margin: 0 0 4px 0;
		}

		.mvbs-notice-message {
			color: #374151;
			margin: 0;
		}

		/* Settings Form */
		.mvbs-settings-form {
			display: flex;
			flex-direction: column;
			gap: 30px;
		}

		/* Settings Sections */
		.mvbs-settings-section {
			background: white;
			border-radius: 16px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			overflow: hidden;
		}

		.mvbs-section-header {
			padding: 24px;
			background: #f8fafc;
			border-bottom: 2px solid #e5e7eb;
		}

		.mvbs-section-title {
			font-size: 20px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 8px 0;
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-section-icon {
			font-size: 24px;
		}

		.mvbs-section-description {
			color: #64748b;
			margin: 0;
			font-size: 14px;
		}

		.mvbs-section-content {
			padding: 24px;
		}

		/* Settings Grid */
		.mvbs-settings-grid {
			display: grid;
			gap: 24px;
		}

		/* Setting Items */
		.mvbs-setting-item {
			display: flex;
			flex-direction: column;
			gap: 12px;
		}

		.mvbs-setting-toggle {
			flex-direction: row;
			align-items: flex-start;
			justify-content: space-between;
			padding: 20px;
			background: #f8fafc;
			border-radius: 12px;
			border: 2px solid #e2e8f0;
			transition: all 0.3s ease;
		}

		.mvbs-setting-toggle:hover {
			border-color: #3b82f6;
			background: #f1f5f9;
		}

		.mvbs-toggle-content {
			flex: 1;
		}

		.mvbs-setting-label {
			display: flex;
			flex-direction: column;
			gap: 4px;
		}

		.mvbs-label-text {
			font-size: 16px;
			font-weight: 600;
			color: #1e293b;
		}

		.mvbs-label-description {
			font-size: 14px;
			color: #64748b;
			line-height: 1.5;
		}

		/* Input Styling */
		.mvbs-input-group {
			position: relative;
			display: flex;
			align-items: center;
		}

		.mvbs-input {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #e2e8f0;
			border-radius: 8px;
			font-size: 14px;
			transition: all 0.3s ease;
			background: white;
		}

		.mvbs-input:focus {
			outline: none;
			border-color: #3b82f6;
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		}

		.mvbs-input-number {
			max-width: 200px;
		}

		.mvbs-input-prefix,
		.mvbs-input-suffix {
			position: absolute;
			color: #64748b;
			font-weight: 600;
			pointer-events: none;
		}

		.mvbs-input-prefix {
			left: 12px;
		}

		.mvbs-input-suffix {
			right: 12px;
		}

		.mvbs-input:has(+ .mvbs-input-suffix) {
			padding-right: 40px;
		}

		.mvbs-input-group:has(.mvbs-input-prefix) .mvbs-input {
			padding-left: 32px;
		}

		/* Toggle Switch */
		.mvbs-toggle-switch {
			position: relative;
			flex-shrink: 0;
		}

		.mvbs-toggle-input {
			position: absolute;
			opacity: 0;
			width: 0;
			height: 0;
			visibility: hidden;
		}

		.mvbs-toggle-label {
			display: block;
			width: 60px;
			height: 32px;
			background: #e2e8f0;
			border-radius: 16px;
			cursor: pointer;
			transition: all 0.3s ease;
			position: relative;
			outline: none;
		}

		.mvbs-toggle-label:focus {
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
		}

		.mvbs-toggle-slider {
			position: absolute;
			top: 4px;
			left: 4px;
			width: 24px;
			height: 24px;
			background: white;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		}

		.mvbs-toggle-input:checked + .mvbs-toggle-label {
			background: #3b82f6;
		}

		.mvbs-toggle-input:checked + .mvbs-toggle-label .mvbs-toggle-slider {
			transform: translateX(28px);
		}

		.mvbs-toggle-input:focus + .mvbs-toggle-label {
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
		}

		/* Settings Footer */
		.mvbs-settings-footer {
			text-align: center;
			padding: 30px;
			background: white;
			border-radius: 16px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-settings .mvbs-btn {
			padding: 12px 20px;
			border: none;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;
			font-size: 14px;
		}

		.mvbs-settings .mvbs-btn-primary {
			background: #3b82f6;
			color: white;
		}

		.mvbs-settings .mvbs-btn-primary:hover {
			background: #2563eb;
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
		}

		.mvbs-settings .mvbs-btn-large {
			padding: 16px 32px;
			font-size: 16px;
			font-weight: 600;
		}

		.mvbs-settings-note {
			margin: 16px 0 0 0;
			color: #64748b;
			font-size: 14px;
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.mvbs-setting-toggle {
				flex-direction: column;
				align-items: stretch;
				gap: 16px;
			}

			.mvbs-toggle-switch {
				align-self: flex-start;
			}

			.mvbs-input-number {
				max-width: 100%;
			}
		}
		</style>
		<?php
	}

	// Messages page removed - private communications between customers and vendors

	/**
	 * Process completed WooCommerce order.
	 *
	 * @since    1.0.0
	 * @param    int  $order_id  Order ID.
	 */
	public function process_completed_order($order_id) {
		Multi_Vender_Book_Store_Order::process_order($order_id);
	}

}
