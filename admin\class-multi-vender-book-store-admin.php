<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of this plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add admin hooks
		add_action('admin_menu', array($this, 'add_admin_menu'));
		add_action('admin_init', array($this, 'admin_init'));

		// WooCommerce integration hooks
		add_action('woocommerce_order_status_completed', array($this, 'process_completed_order'));
		add_action('woocommerce_order_status_processing', array($this, 'process_completed_order'));

	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-admin.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-admin.js', array( 'jquery' ), $this->version, false );

	}

	/**
	 * Add admin menu pages.
	 *
	 * @since    1.0.0
	 */
	public function add_admin_menu() {
		// Main menu page
		add_menu_page(
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			'manage_vendors',
			'multi-vendor-store',
			array($this, 'display_admin_page'),
			'dashicons-store',
			30
		);

		// Vendors submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Vendors', 'multi-vender-book-store'),
			__('Vendors', 'multi-vender-book-store'),
			'manage_vendors',
			'multi-vendor-vendors',
			array($this, 'display_vendors_page')
		);

		// Products submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Vendor Products', 'multi-vender-book-store'),
			__('Vendor Products', 'multi-vender-book-store'),
			'approve_vendor_products',
			'multi-vendor-products',
			array($this, 'display_products_page')
		);

		// Commissions submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Commissions', 'multi-vender-book-store'),
			__('Commissions', 'multi-vender-book-store'),
			'manage_vendor_commissions',
			'multi-vendor-commissions',
			array($this, 'display_commissions_page')
		);

		// Settings submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Settings', 'multi-vender-book-store'),
			__('Settings', 'multi-vender-book-store'),
			'manage_options',
			'multi-vendor-settings',
			array($this, 'display_settings_page')
		);
	}

	/**
	 * Initialize admin functionality.
	 *
	 * @since    1.0.0
	 */
	public function admin_init() {
		// Register settings
		register_setting('mvbs_settings', 'mvbs_default_commission_rate');
		register_setting('mvbs_settings', 'mvbs_buyer_markup_rate');
		register_setting('mvbs_settings', 'mvbs_auto_approve_vendors');
		register_setting('mvbs_settings', 'mvbs_auto_approve_products');
		register_setting('mvbs_settings', 'mvbs_minimum_payout_amount');
	}

	/**
	 * Display main admin page.
	 *
	 * @since    1.0.0
	 */
	public function display_admin_page() {
		$analytics = Multi_Vender_Book_Store_Analytics::get_dashboard_analytics(array('period' => 30));
		$review_stats = Multi_Vender_Book_Store_Review::get_review_statistics(array('period' => 30));

		?>
		<div class="wrap mvbs-admin-dashboard">
			<div class="mvbs-admin-header">
				<h1><?php _e('Multi-Vendor Marketplace Dashboard', 'multi-vender-book-store'); ?></h1>
				<p class="mvbs-admin-subtitle"><?php _e('Comprehensive overview of your marketplace performance', 'multi-vender-book-store'); ?></p>
			</div>

			<div class="mvbs-admin-stats-grid">
				<div class="mvbs-admin-stat-card mvbs-stat-primary">
					<div class="mvbs-stat-icon">🏪</div>
					<div class="mvbs-stat-content">
						<h3><?php echo number_format($analytics['overview']['total_vendors']); ?></h3>
						<p><?php _e('Active Vendors', 'multi-vender-book-store'); ?></p>
						<?php if ($analytics['overview']['pending_vendors'] > 0): ?>
							<span class="mvbs-stat-badge"><?php echo $analytics['overview']['pending_vendors']; ?> <?php _e('pending', 'multi-vender-book-store'); ?></span>
						<?php endif; ?>
					</div>
				</div>

				<div class="mvbs-admin-stat-card mvbs-stat-success">
					<div class="mvbs-stat-icon">📚</div>
					<div class="mvbs-stat-content">
						<h3><?php echo number_format($analytics['overview']['total_products']); ?></h3>
						<p><?php _e('Products Listed', 'multi-vender-book-store'); ?></p>
						<?php if ($analytics['overview']['pending_products'] > 0): ?>
							<span class="mvbs-stat-badge"><?php echo $analytics['overview']['pending_products']; ?> <?php _e('pending', 'multi-vender-book-store'); ?></span>
						<?php endif; ?>
					</div>
				</div>

				<div class="mvbs-admin-stat-card mvbs-stat-info">
					<div class="mvbs-stat-icon">💰</div>
					<div class="mvbs-stat-content">
						<h3>$<?php echo number_format($analytics['overview']['total_sales'], 2); ?></h3>
						<p><?php _e('Total Sales (30 days)', 'multi-vender-book-store'); ?></p>
						<span class="mvbs-stat-detail"><?php echo $analytics['overview']['total_orders']; ?> <?php _e('orders', 'multi-vender-book-store'); ?></span>
					</div>
				</div>

				<div class="mvbs-admin-stat-card mvbs-stat-warning">
					<div class="mvbs-stat-icon">⭐</div>
					<div class="mvbs-stat-content">
						<h3><?php echo number_format($review_stats['overall_stats']->total_reviews); ?></h3>
						<p><?php _e('Customer Reviews', 'multi-vender-book-store'); ?></p>
						<span class="mvbs-stat-detail"><?php echo number_format($review_stats['overall_stats']->overall_avg_rating, 1); ?> <?php _e('avg rating', 'multi-vender-book-store'); ?></span>
					</div>
				</div>
			</div>

			<div class="mvbs-admin-content-grid">
				<div class="mvbs-admin-panel">
					<div class="mvbs-panel-header">
						<h2><?php _e('Recent Activity', 'multi-vender-book-store'); ?></h2>
						<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-admin-btn mvbs-btn-primary"><?php _e('Manage Vendors', 'multi-vender-book-store'); ?></a>
					</div>
					<div class="mvbs-panel-content">
						<?php if (!empty($analytics['sales']['vendor_sales'])): ?>
							<div class="mvbs-vendor-performance">
								<h4><?php _e('Top Performing Vendors', 'multi-vender-book-store'); ?></h4>
								<?php foreach (array_slice($analytics['sales']['vendor_sales'], 0, 5) as $vendor): ?>
									<div class="mvbs-vendor-item">
										<div class="mvbs-vendor-info">
											<strong><?php echo esc_html($vendor->business_name); ?></strong>
											<span class="mvbs-vendor-sales">$<?php echo number_format($vendor->total_sales, 2); ?></span>
										</div>
										<div class="mvbs-vendor-meta">
											<?php echo $vendor->orders; ?> <?php _e('orders', 'multi-vender-book-store'); ?>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<div class="mvbs-empty-state">
								<p><?php _e('No vendor activity yet.', 'multi-vender-book-store'); ?></p>
							</div>
						<?php endif; ?>
					</div>
				</div>

				<div class="mvbs-admin-panel">
					<div class="mvbs-panel-header">
						<h2><?php _e('Quick Actions', 'multi-vender-book-store'); ?></h2>
					</div>
					<div class="mvbs-panel-content">
						<div class="mvbs-quick-actions">
							<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon">👥</div>
								<div class="mvbs-action-content">
									<h4><?php _e('Manage Vendors', 'multi-vender-book-store'); ?></h4>
									<p><?php _e('Approve, review, and manage vendor accounts', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-products'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon">📦</div>
								<div class="mvbs-action-content">
									<h4><?php _e('Review Products', 'multi-vender-book-store'); ?></h4>
									<p><?php _e('Approve and manage vendor product listings', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-commissions'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon">💳</div>
								<div class="mvbs-action-content">
									<h4><?php _e('Commission Reports', 'multi-vender-book-store'); ?></h4>
									<p><?php _e('Track earnings and commission payments', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-settings'); ?>" class="mvbs-quick-action">
								<div class="mvbs-action-icon">⚙️</div>
								<div class="mvbs-action-content">
									<h4><?php _e('Settings', 'multi-vender-book-store'); ?></h4>
									<p><?php _e('Configure marketplace settings and options', 'multi-vender-book-store'); ?></p>
								</div>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<?php
	}

	/**
	 * Display vendors management page with modern UI.
	 *
	 * @since    1.0.0
	 */
	public function display_vendors_page() {
		// Handle vendor actions
		if (isset($_POST['action']) && isset($_POST['vendor_id'])) {
			$vendor_id = intval($_POST['vendor_id']);
			$action = sanitize_text_field($_POST['action']);

			switch ($action) {
				case 'approve':
					if (Multi_Vender_Book_Store_Vendor::approve_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor approved successfully.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'reject':
					if (Multi_Vender_Book_Store_Vendor::reject_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor rejected.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'suspend':
					if (Multi_Vender_Book_Store_Vendor::suspend_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor suspended.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
			}
		}

		// Get vendors with filtering
		$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
		$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

		$vendors = Multi_Vender_Book_Store_Database::get_vendors(array(
			'status' => $status_filter,
			'search' => $search,
			'limit' => 50
		));

		// Get status counts
		$status_counts = Multi_Vender_Book_Store_Database::get_vendor_status_counts();

		?>
		<div class="wrap mvbs-admin-vendors">
			<div class="mvbs-admin-header">
				<h1><?php _e('Vendor Management', 'multi-vender-book-store'); ?></h1>
				<p class="mvbs-admin-subtitle"><?php _e('Manage vendor accounts, approvals, and performance', 'multi-vender-book-store'); ?></p>
			</div>

			<div class="mvbs-admin-filters">
				<div class="mvbs-filter-tabs">
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="mvbs-filter-tab <?php echo empty($status_filter) ? 'active' : ''; ?>">
						<?php _e('All', 'multi-vender-book-store'); ?> (<?php echo array_sum($status_counts); ?>)
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=pending'); ?>" class="mvbs-filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
						<?php _e('Pending', 'multi-vender-book-store'); ?> (<?php echo $status_counts['pending'] ?? 0; ?>)
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=approved'); ?>" class="mvbs-filter-tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
						<?php _e('Approved', 'multi-vender-book-store'); ?> (<?php echo $status_counts['approved'] ?? 0; ?>)
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=suspended'); ?>" class="mvbs-filter-tab <?php echo $status_filter === 'suspended' ? 'active' : ''; ?>">
						<?php _e('Suspended', 'multi-vender-book-store'); ?> (<?php echo $status_counts['suspended'] ?? 0; ?>)
					</a>
				</div>

				<div class="mvbs-search-box">
					<form method="get" action="">
						<input type="hidden" name="page" value="mvbs-vendors">
						<?php if ($status_filter): ?>
							<input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
						<?php endif; ?>
						<input type="search" name="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Search vendors...', 'multi-vender-book-store'); ?>">
						<button type="submit" class="mvbs-admin-btn mvbs-btn-secondary"><?php _e('Search', 'multi-vender-book-store'); ?></button>
					</form>
				</div>
			</div>

			<?php if (empty($vendors)): ?>
				<div class="mvbs-admin-empty-state">
					<div class="mvbs-empty-icon">🏪</div>
					<h3><?php _e('No Vendors Found', 'multi-vender-book-store'); ?></h3>
					<p><?php _e('No vendors match your current filters.', 'multi-vender-book-store'); ?></p>
				</div>
			<?php else: ?>
				<div class="mvbs-vendors-grid">
					<?php foreach ($vendors as $vendor): ?>
						<div class="mvbs-vendor-card mvbs-status-<?php echo esc_attr($vendor->status); ?>">
							<div class="mvbs-vendor-header">
								<div class="mvbs-vendor-avatar">
									<span class="mvbs-avatar-icon">🏪</span>
								</div>
								<div class="mvbs-vendor-info">
									<h3><?php echo esc_html($vendor->business_name); ?></h3>
									<p class="mvbs-vendor-email"><?php echo esc_html($vendor->business_email); ?></p>
									<span class="mvbs-vendor-status mvbs-status-<?php echo esc_attr($vendor->status); ?>">
										<?php echo ucfirst($vendor->status); ?>
									</span>
								</div>
							</div>

							<div class="mvbs-vendor-stats">
								<div class="mvbs-vendor-stat">
									<span class="mvbs-stat-number"><?php echo Multi_Vender_Book_Store_Database::get_vendor_product_count($vendor->id); ?></span>
									<span class="mvbs-stat-label"><?php _e('Products', 'multi-vender-book-store'); ?></span>
								</div>
								<div class="mvbs-vendor-stat">
									<span class="mvbs-stat-number"><?php echo number_format($vendor->average_rating, 1); ?>⭐</span>
									<span class="mvbs-stat-label"><?php _e('Rating', 'multi-vender-book-store'); ?></span>
								</div>
								<div class="mvbs-vendor-stat">
									<span class="mvbs-stat-number"><?php echo date('M Y', strtotime($vendor->created_at)); ?></span>
									<span class="mvbs-stat-label"><?php _e('Joined', 'multi-vender-book-store'); ?></span>
								</div>
							</div>

							<div class="mvbs-vendor-details">
								<?php if ($vendor->business_phone): ?>
									<p><strong><?php _e('Phone:', 'multi-vender-book-store'); ?></strong> <?php echo esc_html($vendor->business_phone); ?></p>
								<?php endif; ?>
								<?php if ($vendor->business_address): ?>
									<p><strong><?php _e('Address:', 'multi-vender-book-store'); ?></strong> <?php echo esc_html($vendor->business_address); ?></p>
								<?php endif; ?>
							</div>

							<div class="mvbs-vendor-actions">
								<?php
								$dashboard_url = add_query_arg(array(
									'mvbs_admin_view' => 'vendor_dashboard',
									'vendor_id' => $vendor->id
								), get_permalink(get_option('mvbs_vendor_dashboard_page_id')));
								?>
								<a href="<?php echo esc_url($dashboard_url); ?>" class="mvbs-admin-btn mvbs-btn-secondary mvbs-btn-small" target="_blank">
									<?php _e('View Dashboard', 'multi-vender-book-store'); ?>
								</a>

								<?php if ($vendor->status === 'pending'): ?>
									<form method="post" style="display: inline;">
										<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
										<input type="hidden" name="action" value="approve">
										<button type="submit" class="mvbs-admin-btn mvbs-btn-success mvbs-btn-small" onclick="return confirm('<?php _e('Approve this vendor?', 'multi-vender-book-store'); ?>')">
											<?php _e('Approve', 'multi-vender-book-store'); ?>
										</button>
									</form>
									<form method="post" style="display: inline;">
										<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
										<input type="hidden" name="action" value="reject">
										<button type="submit" class="mvbs-admin-btn mvbs-btn-danger mvbs-btn-small" onclick="return confirm('<?php _e('Reject this vendor?', 'multi-vender-book-store'); ?>')">
											<?php _e('Reject', 'multi-vender-book-store'); ?>
										</button>
									</form>
								<?php elseif ($vendor->status === 'approved'): ?>
									<form method="post" style="display: inline;">
										<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
										<input type="hidden" name="action" value="suspend">
										<button type="submit" class="mvbs-admin-btn mvbs-btn-warning mvbs-btn-small" onclick="return confirm('<?php _e('Suspend this vendor?', 'multi-vender-book-store'); ?>')">
											<?php _e('Suspend', 'multi-vender-book-store'); ?>
										</button>
									</form>
								<?php elseif ($vendor->status === 'suspended'): ?>
									<form method="post" style="display: inline;">
										<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
										<input type="hidden" name="action" value="approve">
										<button type="submit" class="mvbs-admin-btn mvbs-btn-success mvbs-btn-small" onclick="return confirm('<?php _e('Reactivate this vendor?', 'multi-vender-book-store'); ?>')">
											<?php _e('Reactivate', 'multi-vender-book-store'); ?>
										</button>
									</form>
								<?php endif; ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php
	}

	/**
	 * Display products management page.
	 *
	 * @since    1.0.0
	 */
	public function display_products_page() {
		// Handle product approval
		if (isset($_POST['approve_product']) && isset($_POST['product_id'])) {
			$product_id = intval($_POST['product_id']);
			if (Multi_Vender_Book_Store_Product::approve_product($product_id)) {
				echo '<div class="notice notice-success"><p>' . __('Product approved successfully.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get pending products
		global $wpdb;
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$pending_products = $wpdb->get_results(
			"SELECT vp.*, p.post_title, p.post_content, v.business_name
			FROM $vendor_products_table vp
			JOIN {$wpdb->posts} p ON vp.product_id = p.ID
			JOIN {$wpdb->prefix}mvbs_vendors v ON vp.vendor_id = v.id
			WHERE vp.status = 'pending'
			ORDER BY vp.created_at DESC
			LIMIT 50"
		);

		echo '<div class="wrap">';
		echo '<h1>' . __('Vendor Products', 'multi-vender-book-store') . '</h1>';

		if (empty($pending_products)) {
			echo '<p>' . __('No pending products for review.', 'multi-vender-book-store') . '</p>';
		} else {
			echo '<table class="wp-list-table widefat fixed striped">';
			echo '<thead>';
			echo '<tr>';
			echo '<th>' . __('Product', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Vendor', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Price', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Condition', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Submitted', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Actions', 'multi-vender-book-store') . '</th>';
			echo '</tr>';
			echo '</thead>';
			echo '<tbody>';

			foreach ($pending_products as $product) {
				$price = get_post_meta($product->product_id, '_regular_price', true);
				$condition = get_post_meta($product->product_id, 'book_condition', true);

				echo '<tr>';
				echo '<td>';
				echo '<strong>' . esc_html($product->post_title) . '</strong><br>';
				echo '<small>' . wp_trim_words($product->post_content, 15) . '</small>';
				echo '</td>';
				echo '<td>' . esc_html($product->business_name) . '</td>';
				echo '<td>$' . number_format(floatval($price), 2) . '</td>';
				echo '<td>' . ucfirst(str_replace('_', ' ', $condition)) . '</td>';
				echo '<td>' . date('M j, Y', strtotime($product->created_at)) . '</td>';
				echo '<td>';
				echo '<form method="post" style="display:inline;">';
				echo '<input type="hidden" name="product_id" value="' . $product->product_id . '">';
				echo '<input type="submit" name="approve_product" value="' . __('Approve', 'multi-vender-book-store') . '" class="button button-primary button-small">';
				echo '</form> ';
				echo '<a href="' . get_edit_post_link($product->product_id) . '" class="button button-small">' . __('Edit', 'multi-vender-book-store') . '</a>';
				echo '</td>';
				echo '</tr>';
			}

			echo '</tbody>';
			echo '</table>';
		}

		echo '</div>';
	}

	/**
	 * Display commissions page.
	 *
	 * @since    1.0.0
	 */
	public function display_commissions_page() {
		echo '<div class="wrap">';
		echo '<h1>' . __('Commission Management', 'multi-vender-book-store') . '</h1>';
		echo '<p>' . __('Track and manage vendor commissions.', 'multi-vender-book-store') . '</p>';
		echo '</div>';
	}

	/**
	 * Display settings page.
	 *
	 * @since    1.0.0
	 */
	public function display_settings_page() {
		if (isset($_POST['submit'])) {
			update_option('mvbs_default_commission_rate', sanitize_text_field($_POST['mvbs_default_commission_rate']));
			update_option('mvbs_buyer_markup_rate', sanitize_text_field($_POST['mvbs_buyer_markup_rate']));
			update_option('mvbs_auto_approve_vendors', isset($_POST['mvbs_auto_approve_vendors']) ? 'yes' : 'no');
			update_option('mvbs_auto_approve_products', isset($_POST['mvbs_auto_approve_products']) ? 'yes' : 'no');
			update_option('mvbs_minimum_payout_amount', sanitize_text_field($_POST['mvbs_minimum_payout_amount']));

			echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'multi-vender-book-store') . '</p></div>';
		}

		$commission_rate = get_option('mvbs_default_commission_rate', '10.00');
		$markup_rate = get_option('mvbs_buyer_markup_rate', '5.00');
		$auto_approve_vendors = get_option('mvbs_auto_approve_vendors', 'no');
		$auto_approve_products = get_option('mvbs_auto_approve_products', 'no');
		$minimum_payout = get_option('mvbs_minimum_payout_amount', '50.00');

		echo '<div class="wrap">';
		echo '<h1>' . __('Multi-Vendor Store Settings', 'multi-vender-book-store') . '</h1>';
		echo '<form method="post" action="">';
		echo '<table class="form-table">';

		echo '<tr>';
		echo '<th scope="row">' . __('Default Commission Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_default_commission_rate" value="' . esc_attr($commission_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Buyer Markup Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_buyer_markup_rate" value="' . esc_attr($markup_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Vendors', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_vendors" ' . checked($auto_approve_vendors, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Products', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_products" ' . checked($auto_approve_products, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Minimum Payout Amount', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_minimum_payout_amount" value="' . esc_attr($minimum_payout) . '" /></td>';
		echo '</tr>';

		echo '</table>';
		echo '<p class="submit"><input type="submit" name="submit" class="button-primary" value="' . __('Save Settings', 'multi-vender-book-store') . '" /></p>';
		echo '</form>';
		echo '</div>';
	}

	/**
	 * Process completed WooCommerce order.
	 *
	 * @since    1.0.0
	 * @param    int  $order_id  Order ID.
	 */
	public function process_completed_order($order_id) {
		Multi_Vender_Book_Store_Order::process_order($order_id);
	}

}
