<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of this plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add admin hooks
		add_action('admin_menu', array($this, 'add_admin_menu'));
		add_action('admin_init', array($this, 'admin_init'));

	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-admin.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-admin.js', array( 'jquery' ), $this->version, false );

	}

	/**
	 * Add admin menu pages.
	 *
	 * @since    1.0.0
	 */
	public function add_admin_menu() {
		// Main menu page
		add_menu_page(
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			'manage_vendors',
			'multi-vendor-store',
			array($this, 'display_admin_page'),
			'dashicons-store',
			30
		);

		// Vendors submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Vendors', 'multi-vender-book-store'),
			__('Vendors', 'multi-vender-book-store'),
			'manage_vendors',
			'multi-vendor-vendors',
			array($this, 'display_vendors_page')
		);

		// Products submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Vendor Products', 'multi-vender-book-store'),
			__('Vendor Products', 'multi-vender-book-store'),
			'approve_vendor_products',
			'multi-vendor-products',
			array($this, 'display_products_page')
		);

		// Commissions submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Commissions', 'multi-vender-book-store'),
			__('Commissions', 'multi-vender-book-store'),
			'manage_vendor_commissions',
			'multi-vendor-commissions',
			array($this, 'display_commissions_page')
		);

		// Settings submenu
		add_submenu_page(
			'multi-vendor-store',
			__('Settings', 'multi-vender-book-store'),
			__('Settings', 'multi-vender-book-store'),
			'manage_options',
			'multi-vendor-settings',
			array($this, 'display_settings_page')
		);
	}

	/**
	 * Initialize admin functionality.
	 *
	 * @since    1.0.0
	 */
	public function admin_init() {
		// Register settings
		register_setting('mvbs_settings', 'mvbs_default_commission_rate');
		register_setting('mvbs_settings', 'mvbs_buyer_markup_rate');
		register_setting('mvbs_settings', 'mvbs_auto_approve_vendors');
		register_setting('mvbs_settings', 'mvbs_auto_approve_products');
		register_setting('mvbs_settings', 'mvbs_minimum_payout_amount');
	}

	/**
	 * Display main admin page.
	 *
	 * @since    1.0.0
	 */
	public function display_admin_page() {
		echo '<div class="wrap">';
		echo '<h1>' . __('Multi-Vendor Book Store Dashboard', 'multi-vender-book-store') . '</h1>';
		echo '<p>' . __('Welcome to the Multi-Vendor Book Store management dashboard.', 'multi-vender-book-store') . '</p>';
		echo '</div>';
	}

	/**
	 * Display vendors management page.
	 *
	 * @since    1.0.0
	 */
	public function display_vendors_page() {
		// Handle vendor approval/rejection
		if (isset($_POST['approve_vendor']) && isset($_POST['vendor_id'])) {
			$vendor_id = intval($_POST['vendor_id']);
			if (Multi_Vender_Book_Store_Vendor::approve_vendor($vendor_id)) {
				echo '<div class="notice notice-success"><p>' . __('Vendor approved successfully.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get vendors
		$vendors = Multi_Vender_Book_Store_Database::get_vendors(array('limit' => 50));

		echo '<div class="wrap">';
		echo '<h1>' . __('Vendor Management', 'multi-vender-book-store') . '</h1>';

		if (empty($vendors)) {
			echo '<p>' . __('No vendors found.', 'multi-vender-book-store') . '</p>';
		} else {
			echo '<table class="wp-list-table widefat fixed striped">';
			echo '<thead>';
			echo '<tr>';
			echo '<th>' . __('Business Name', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Email', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Status', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Registration Date', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Actions', 'multi-vender-book-store') . '</th>';
			echo '</tr>';
			echo '</thead>';
			echo '<tbody>';

			foreach ($vendors as $vendor) {
				$user = get_user_by('ID', $vendor->user_id);
				echo '<tr>';
				echo '<td><strong>' . esc_html($vendor->business_name) . '</strong></td>';
				echo '<td>' . esc_html($vendor->business_email) . '</td>';
				echo '<td>';
				$status_class = 'mvbs-status-' . $vendor->status;
				echo '<span class="' . $status_class . '">' . ucfirst($vendor->status) . '</span>';
				echo '</td>';
				echo '<td>' . date('M j, Y', strtotime($vendor->created_at)) . '</td>';
				echo '<td>';
				if ($vendor->status === 'pending') {
					echo '<form method="post" style="display:inline;">';
					echo '<input type="hidden" name="vendor_id" value="' . $vendor->id . '">';
					echo '<input type="submit" name="approve_vendor" value="' . __('Approve', 'multi-vender-book-store') . '" class="button button-primary button-small">';
					echo '</form>';
				}
				echo '</td>';
				echo '</tr>';
			}

			echo '</tbody>';
			echo '</table>';
		}

		echo '</div>';
	}

	/**
	 * Display products management page.
	 *
	 * @since    1.0.0
	 */
	public function display_products_page() {
		// Handle product approval
		if (isset($_POST['approve_product']) && isset($_POST['product_id'])) {
			$product_id = intval($_POST['product_id']);
			if (Multi_Vender_Book_Store_Product::approve_product($product_id)) {
				echo '<div class="notice notice-success"><p>' . __('Product approved successfully.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get pending products
		global $wpdb;
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$pending_products = $wpdb->get_results(
			"SELECT vp.*, p.post_title, p.post_content, v.business_name
			FROM $vendor_products_table vp
			JOIN {$wpdb->posts} p ON vp.product_id = p.ID
			JOIN {$wpdb->prefix}mvbs_vendors v ON vp.vendor_id = v.id
			WHERE vp.status = 'pending'
			ORDER BY vp.created_at DESC
			LIMIT 50"
		);

		echo '<div class="wrap">';
		echo '<h1>' . __('Vendor Products', 'multi-vender-book-store') . '</h1>';

		if (empty($pending_products)) {
			echo '<p>' . __('No pending products for review.', 'multi-vender-book-store') . '</p>';
		} else {
			echo '<table class="wp-list-table widefat fixed striped">';
			echo '<thead>';
			echo '<tr>';
			echo '<th>' . __('Product', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Vendor', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Price', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Condition', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Submitted', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Actions', 'multi-vender-book-store') . '</th>';
			echo '</tr>';
			echo '</thead>';
			echo '<tbody>';

			foreach ($pending_products as $product) {
				$price = get_post_meta($product->product_id, '_regular_price', true);
				$condition = get_post_meta($product->product_id, 'book_condition', true);

				echo '<tr>';
				echo '<td>';
				echo '<strong>' . esc_html($product->post_title) . '</strong><br>';
				echo '<small>' . wp_trim_words($product->post_content, 15) . '</small>';
				echo '</td>';
				echo '<td>' . esc_html($product->business_name) . '</td>';
				echo '<td>$' . number_format(floatval($price), 2) . '</td>';
				echo '<td>' . ucfirst(str_replace('_', ' ', $condition)) . '</td>';
				echo '<td>' . date('M j, Y', strtotime($product->created_at)) . '</td>';
				echo '<td>';
				echo '<form method="post" style="display:inline;">';
				echo '<input type="hidden" name="product_id" value="' . $product->product_id . '">';
				echo '<input type="submit" name="approve_product" value="' . __('Approve', 'multi-vender-book-store') . '" class="button button-primary button-small">';
				echo '</form> ';
				echo '<a href="' . get_edit_post_link($product->product_id) . '" class="button button-small">' . __('Edit', 'multi-vender-book-store') . '</a>';
				echo '</td>';
				echo '</tr>';
			}

			echo '</tbody>';
			echo '</table>';
		}

		echo '</div>';
	}

	/**
	 * Display commissions page.
	 *
	 * @since    1.0.0
	 */
	public function display_commissions_page() {
		echo '<div class="wrap">';
		echo '<h1>' . __('Commission Management', 'multi-vender-book-store') . '</h1>';
		echo '<p>' . __('Track and manage vendor commissions.', 'multi-vender-book-store') . '</p>';
		echo '</div>';
	}

	/**
	 * Display settings page.
	 *
	 * @since    1.0.0
	 */
	public function display_settings_page() {
		if (isset($_POST['submit'])) {
			update_option('mvbs_default_commission_rate', sanitize_text_field($_POST['mvbs_default_commission_rate']));
			update_option('mvbs_buyer_markup_rate', sanitize_text_field($_POST['mvbs_buyer_markup_rate']));
			update_option('mvbs_auto_approve_vendors', isset($_POST['mvbs_auto_approve_vendors']) ? 'yes' : 'no');
			update_option('mvbs_auto_approve_products', isset($_POST['mvbs_auto_approve_products']) ? 'yes' : 'no');
			update_option('mvbs_minimum_payout_amount', sanitize_text_field($_POST['mvbs_minimum_payout_amount']));

			echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'multi-vender-book-store') . '</p></div>';
		}

		$commission_rate = get_option('mvbs_default_commission_rate', '10.00');
		$markup_rate = get_option('mvbs_buyer_markup_rate', '5.00');
		$auto_approve_vendors = get_option('mvbs_auto_approve_vendors', 'no');
		$auto_approve_products = get_option('mvbs_auto_approve_products', 'no');
		$minimum_payout = get_option('mvbs_minimum_payout_amount', '50.00');

		echo '<div class="wrap">';
		echo '<h1>' . __('Multi-Vendor Store Settings', 'multi-vender-book-store') . '</h1>';
		echo '<form method="post" action="">';
		echo '<table class="form-table">';

		echo '<tr>';
		echo '<th scope="row">' . __('Default Commission Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_default_commission_rate" value="' . esc_attr($commission_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Buyer Markup Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_buyer_markup_rate" value="' . esc_attr($markup_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Vendors', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_vendors" ' . checked($auto_approve_vendors, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Products', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_products" ' . checked($auto_approve_products, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Minimum Payout Amount', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_minimum_payout_amount" value="' . esc_attr($minimum_payout) . '" /></td>';
		echo '</tr>';

		echo '</table>';
		echo '<p class="submit"><input type="submit" name="submit" class="button-primary" value="' . __('Save Settings', 'multi-vender-book-store') . '" /></p>';
		echo '</form>';
		echo '</div>';
	}

}
