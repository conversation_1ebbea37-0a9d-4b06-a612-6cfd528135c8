<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/admin
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of this plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add admin hooks
		add_action('admin_menu', array($this, 'add_admin_menu'));
		add_action('admin_init', array($this, 'admin_init'));

		// WooCommerce integration hooks
		add_action('woocommerce_order_status_completed', array($this, 'process_completed_order'));
		add_action('woocommerce_order_status_processing', array($this, 'process_completed_order'));

	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-admin.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-admin.js', array( 'jquery' ), $this->version, false );

	}

	/**
	 * Add admin menu pages.
	 *
	 * @since    1.0.0
	 */
	public function add_admin_menu() {
		// Main menu page
		add_menu_page(
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			__('Multi-Vendor Store', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-dashboard',
			array($this, 'display_admin_page'),
			'dashicons-store',
			30
		);

		// Vendors submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Vendors', 'multi-vender-book-store'),
			__('Vendors', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-vendors',
			array($this, 'display_vendors_page')
		);

		// Products submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Vendor Products', 'multi-vender-book-store'),
			__('Vendor Products', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-products',
			array($this, 'display_products_page')
		);

		// Commissions submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Commissions', 'multi-vender-book-store'),
			__('Commissions', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-commissions',
			array($this, 'display_commissions_page')
		);

		// Messages submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Messages', 'multi-vender-book-store'),
			__('Messages', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-messages',
			array($this, 'display_messages_page')
		);

		// Settings submenu
		add_submenu_page(
			'mvbs-dashboard',
			__('Settings', 'multi-vender-book-store'),
			__('Settings', 'multi-vender-book-store'),
			'manage_options',
			'mvbs-settings',
			array($this, 'display_settings_page')
		);
	}

	/**
	 * Initialize admin functionality.
	 *
	 * @since    1.0.0
	 */
	public function admin_init() {
		// Register settings
		register_setting('mvbs_settings', 'mvbs_default_commission_rate');
		register_setting('mvbs_settings', 'mvbs_buyer_markup_rate');
		register_setting('mvbs_settings', 'mvbs_auto_approve_vendors');
		register_setting('mvbs_settings', 'mvbs_auto_approve_products');
		register_setting('mvbs_settings', 'mvbs_minimum_payout_amount');
	}

	/**
	 * Display main admin page.
	 *
	 * @since    1.0.0
	 */
	public function display_admin_page() {
		// Add debug info
		echo '<!-- MVBS Plugin Debug: Admin page loaded successfully -->';

		$analytics = Multi_Vender_Book_Store_Analytics::get_dashboard_analytics(array('period' => 30));
		$review_stats = Multi_Vender_Book_Store_Review::get_review_statistics(array('period' => 30));

		?>
		<div class="wrap">
			<h1><?php _e('Multi-Vendor Marketplace Dashboard', 'multi-vender-book-store'); ?></h1>
			<p><?php _e('Comprehensive overview of your marketplace performance', 'multi-vender-book-store'); ?></p>

			<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
				<div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; text-align: center;">
					<div style="font-size: 32px; margin-bottom: 10px;">🏪</div>
					<h3 style="margin: 0; font-size: 28px; color: #0073aa;"><?php echo number_format($analytics['overview']['total_vendors']); ?></h3>
					<p style="margin: 5px 0; color: #666;"><?php _e('Active Vendors', 'multi-vender-book-store'); ?></p>
					<?php if ($analytics['overview']['pending_vendors'] > 0): ?>
						<span style="background: #fbbf24; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">
							<?php echo $analytics['overview']['pending_vendors']; ?> <?php _e('pending', 'multi-vender-book-store'); ?>
						</span>
					<?php endif; ?>
				</div>

				<div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; text-align: center;">
					<div style="font-size: 32px; margin-bottom: 10px;">📚</div>
					<h3 style="margin: 0; font-size: 28px; color: #10b981;"><?php echo number_format($analytics['overview']['total_products']); ?></h3>
					<p style="margin: 5px 0; color: #666;"><?php _e('Products Listed', 'multi-vender-book-store'); ?></p>
					<?php if ($analytics['overview']['pending_products'] > 0): ?>
						<span style="background: #fbbf24; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">
							<?php echo $analytics['overview']['pending_products']; ?> <?php _e('pending', 'multi-vender-book-store'); ?>
						</span>
					<?php endif; ?>
				</div>

				<div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; text-align: center;">
					<div style="font-size: 32px; margin-bottom: 10px;">💰</div>
					<h3 style="margin: 0; font-size: 28px; color: #3b82f6;">$<?php echo number_format($analytics['overview']['total_sales'], 2); ?></h3>
					<p style="margin: 5px 0; color: #666;"><?php _e('Total Sales (30 days)', 'multi-vender-book-store'); ?></p>
					<small style="color: #6b7280;"><?php echo $analytics['overview']['total_orders']; ?> <?php _e('orders', 'multi-vender-book-store'); ?></small>
				</div>

				<div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; text-align: center;">
					<div style="font-size: 32px; margin-bottom: 10px;">⭐</div>
					<h3 style="margin: 0; font-size: 28px; color: #f59e0b;"><?php echo number_format($review_stats['overall_stats']->total_reviews); ?></h3>
					<p style="margin: 5px 0; color: #666;"><?php _e('Customer Reviews', 'multi-vender-book-store'); ?></p>
					<small style="color: #6b7280;"><?php echo number_format($review_stats['overall_stats']->overall_avg_rating, 1); ?> <?php _e('avg rating', 'multi-vender-book-store'); ?></small>
				</div>
			</div>

			<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin: 20px 0;">
				<div style="background: white; border: 1px solid #ccd0d4; border-radius: 4px;">
					<div style="padding: 20px; border-bottom: 1px solid #f1f5f9; display: flex; justify-content: space-between; align-items: center;">
						<h2 style="margin: 0;"><?php _e('Recent Activity', 'multi-vender-book-store'); ?></h2>
						<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" class="button button-primary"><?php _e('Manage Vendors', 'multi-vender-book-store'); ?></a>
					</div>
					<div style="padding: 20px;">
						<?php if (!empty($analytics['sales']['vendor_sales'])): ?>
							<h4><?php _e('Top Performing Vendors', 'multi-vender-book-store'); ?></h4>
							<?php foreach (array_slice($analytics['sales']['vendor_sales'], 0, 5) as $vendor): ?>
								<div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
									<div>
										<strong><?php echo esc_html($vendor->business_name); ?></strong>
										<br><small style="color: #666;"><?php echo $vendor->orders; ?> <?php _e('orders', 'multi-vender-book-store'); ?></small>
									</div>
									<div style="color: #10b981; font-weight: 600;">
										$<?php echo number_format($vendor->total_sales, 2); ?>
									</div>
								</div>
							<?php endforeach; ?>
						<?php else: ?>
							<p style="text-align: center; color: #666; font-style: italic;"><?php _e('No vendor activity yet.', 'multi-vender-book-store'); ?></p>
						<?php endif; ?>
					</div>
				</div>

				<div style="background: white; border: 1px solid #ccd0d4; border-radius: 4px;">
					<div style="padding: 20px; border-bottom: 1px solid #f1f5f9;">
						<h2 style="margin: 0;"><?php _e('Quick Actions', 'multi-vender-book-store'); ?></h2>
					</div>
					<div style="padding: 20px;">
						<div style="display: flex; flex-direction: column; gap: 16px;">
							<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; text-decoration: none; border: 2px solid transparent;" onmouseover="this.style.background='#e2e8f0'; this.style.borderColor='#0073aa';" onmouseout="this.style.background='#f8fafc'; this.style.borderColor='transparent';">
								<div style="font-size: 24px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">👥</div>
								<div>
									<h4 style="margin: 0 0 4px 0; color: #2c3e50;"><?php _e('Manage Vendors', 'multi-vender-book-store'); ?></h4>
									<p style="margin: 0; color: #6b7280; font-size: 14px;"><?php _e('Approve, review, and manage vendor accounts', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-products'); ?>" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; text-decoration: none; border: 2px solid transparent;" onmouseover="this.style.background='#e2e8f0'; this.style.borderColor='#0073aa';" onmouseout="this.style.background='#f8fafc'; this.style.borderColor='transparent';">
								<div style="font-size: 24px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">📦</div>
								<div>
									<h4 style="margin: 0 0 4px 0; color: #2c3e50;"><?php _e('Review Products', 'multi-vender-book-store'); ?></h4>
									<p style="margin: 0; color: #6b7280; font-size: 14px;"><?php _e('Approve and manage vendor product listings', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-commissions'); ?>" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; text-decoration: none; border: 2px solid transparent;" onmouseover="this.style.background='#e2e8f0'; this.style.borderColor='#0073aa';" onmouseout="this.style.background='#f8fafc'; this.style.borderColor='transparent';">
								<div style="font-size: 24px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">💳</div>
								<div>
									<h4 style="margin: 0 0 4px 0; color: #2c3e50;"><?php _e('Commission Reports', 'multi-vender-book-store'); ?></h4>
									<p style="margin: 0; color: #6b7280; font-size: 14px;"><?php _e('Track earnings and commission payments', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-messages'); ?>" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; text-decoration: none; border: 2px solid transparent;" onmouseover="this.style.background='#e2e8f0'; this.style.borderColor='#0073aa';" onmouseout="this.style.background='#f8fafc'; this.style.borderColor='transparent';">
								<div style="font-size: 24px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">💬</div>
								<div>
									<h4 style="margin: 0 0 4px 0; color: #2c3e50;"><?php _e('Messages', 'multi-vender-book-store'); ?></h4>
									<p style="margin: 0; color: #6b7280; font-size: 14px;"><?php _e('Manage vendor-customer communications', 'multi-vender-book-store'); ?></p>
								</div>
							</a>

							<a href="<?php echo admin_url('admin.php?page=mvbs-settings'); ?>" style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; text-decoration: none; border: 2px solid transparent;" onmouseover="this.style.background='#e2e8f0'; this.style.borderColor='#0073aa';" onmouseout="this.style.background='#f8fafc'; this.style.borderColor='transparent';">
								<div style="font-size: 24px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">⚙️</div>
								<div>
									<h4 style="margin: 0 0 4px 0; color: #2c3e50;"><?php _e('Settings', 'multi-vender-book-store'); ?></h4>
									<p style="margin: 0; color: #6b7280; font-size: 14px;"><?php _e('Configure marketplace settings and options', 'multi-vender-book-store'); ?></p>
								</div>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<?php
	}

	/**
	 * Display vendors management page with modern UI.
	 *
	 * @since    1.0.0
	 */
	public function display_vendors_page() {
		// Handle vendor actions
		if (isset($_POST['action']) && isset($_POST['vendor_id'])) {
			$vendor_id = intval($_POST['vendor_id']);
			$action = sanitize_text_field($_POST['action']);

			switch ($action) {
				case 'approve':
					if (Multi_Vender_Book_Store_Vendor::approve_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor approved successfully.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to approve vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'reject':
					if (Multi_Vender_Book_Store_Vendor::reject_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor rejected.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to reject vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
				case 'suspend':
					if (Multi_Vender_Book_Store_Vendor::suspend_vendor($vendor_id)) {
						echo '<div class="notice notice-success is-dismissible"><p>' . __('Vendor suspended.', 'multi-vender-book-store') . '</p></div>';
					} else {
						echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to suspend vendor.', 'multi-vender-book-store') . '</p></div>';
					}
					break;
			}
		}

		// Handle test vendor creation
		if (isset($_POST['create_test_vendor'])) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';

			$result = $wpdb->insert(
				$table_name,
				array(
					'user_id' => get_current_user_id(),
					'business_name' => 'Test Vendor ' . time(),
					'business_email' => '<EMAIL>',
					'business_phone' => '************',
					'business_address' => '123 Test Street',
					'status' => 'pending',
					'created_at' => current_time('mysql')
				),
				array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
			);

			if ($result) {
				echo '<div class="notice notice-success is-dismissible"><p>' . __('Test vendor created successfully.', 'multi-vender-book-store') . '</p></div>';
			} else {
				echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to create test vendor: ', 'multi-vender-book-store') . $wpdb->last_error . '</p></div>';
			}
		}

		// Handle fix empty status
		if (isset($_POST['fix_empty_status'])) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';

			$result = $wpdb->query(
				"UPDATE $table_name SET status = 'pending' WHERE status IS NULL OR status = ''"
			);

			if ($result !== false) {
				echo '<div class="notice notice-success is-dismissible"><p>' . sprintf(__('Fixed %d vendors with empty status.', 'multi-vender-book-store'), $result) . '</p></div>';
			} else {
				echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to fix vendor status.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get vendors with filtering
		$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
		$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

		$vendors = Multi_Vender_Book_Store_Database::get_vendors(array(
			'status' => $status_filter,
			'search' => $search,
			'limit' => 50
		));

		// Ensure all vendors have proper data
		foreach ($vendors as $vendor) {
			if (empty($vendor->status)) {
				$vendor->status = 'pending';
			}
			if (empty($vendor->average_rating)) {
				$vendor->average_rating = 0;
			}
			if (empty($vendor->total_reviews)) {
				$vendor->total_reviews = 0;
			}
		}

		// Get status counts - with fallback for empty results
		$status_counts = Multi_Vender_Book_Store_Database::get_vendor_status_counts();
		if (empty($status_counts)) {
			$status_counts = array('pending' => 0, 'approved' => 0, 'suspended' => 0, 'rejected' => 0);
		}

		// Debug information
		echo '<!-- Debug: Vendors count: ' . count($vendors) . ' -->';
		echo '<!-- Debug: Status counts: ' . print_r($status_counts, true) . ' -->';
		echo '<!-- Debug: Current user can manage options: ' . (current_user_can('manage_options') ? 'yes' : 'no') . ' -->';

		// Show debug info if no vendors
		if (empty($vendors)) {
			global $wpdb;
			$table_name = $wpdb->prefix . 'mvbs_vendors';
			$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
			echo '<div class="notice notice-info"><p><strong>Debug:</strong> No vendors found. Table exists: ' . ($table_exists ? 'yes' : 'no') . '</p></div>';

			if ($table_exists) {
				$count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
				echo '<div class="notice notice-info"><p><strong>Debug:</strong> Total vendors in database: ' . $count . '</p></div>';
			}
		}

		?>
		<div class="wrap mvbs-vendor-management">
			<!-- Modern Header -->
			<div class="mvbs-page-header">
				<div class="mvbs-header-content">
					<h1 class="mvbs-page-title">
						<span class="mvbs-title-icon">👥</span>
						<?php _e('Vendor Management', 'multi-vender-book-store'); ?>
					</h1>
					<p class="mvbs-page-subtitle"><?php _e('Manage vendor accounts, approvals, and performance', 'multi-vender-book-store'); ?></p>
				</div>
				<div class="mvbs-header-actions">
					<form method="post" style="display: inline; margin-right: 10px;">
						<button type="submit" name="fix_empty_status" class="mvbs-btn mvbs-btn-secondary" onclick="return confirm('Fix all vendors with empty status?')">
							<span class="mvbs-btn-icon">🔧</span>
							<?php _e('Fix Status', 'multi-vender-book-store'); ?>
						</button>
					</form>
					<form method="post" style="display: inline;">
						<button type="submit" name="create_test_vendor" class="mvbs-btn mvbs-btn-secondary">
							<span class="mvbs-btn-icon">➕</span>
							<?php _e('Create Test Vendor', 'multi-vender-book-store'); ?>
						</button>
					</form>
				</div>
			</div>

			<!-- Status Filter Tabs -->
			<div class="mvbs-filter-tabs-container">
				<div class="mvbs-filter-tabs">
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors'); ?>"
					   class="mvbs-filter-tab <?php echo empty($status_filter) ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">📊</span>
						<span class="mvbs-tab-text"><?php _e('All', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo array_sum($status_counts); ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=pending'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⏳</span>
						<span class="mvbs-tab-text"><?php _e('Pending', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['pending'] ?? 0; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=approved'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">✅</span>
						<span class="mvbs-tab-text"><?php _e('Approved', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['approved'] ?? 0; ?></span>
					</a>
					<a href="<?php echo admin_url('admin.php?page=mvbs-vendors&status=suspended'); ?>"
					   class="mvbs-filter-tab <?php echo $status_filter === 'suspended' ? 'active' : ''; ?>">
						<span class="mvbs-tab-icon">⛔</span>
						<span class="mvbs-tab-text"><?php _e('Suspended', 'multi-vender-book-store'); ?></span>
						<span class="mvbs-tab-count"><?php echo $status_counts['suspended'] ?? 0; ?></span>
					</a>
				</div>

				<!-- Search Bar -->
				<div class="mvbs-search-container">
					<form method="get" action="" class="mvbs-search-form">
						<input type="hidden" name="page" value="mvbs-vendors">
						<?php if ($status_filter): ?>
							<input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
						<?php endif; ?>
						<div class="mvbs-search-input-group">
							<span class="mvbs-search-icon">🔍</span>
							<input type="search" name="search" value="<?php echo esc_attr($search); ?>"
								   placeholder="<?php _e('Search vendors by name or email...', 'multi-vender-book-store'); ?>"
								   class="mvbs-search-input">
							<button type="submit" class="mvbs-search-btn"><?php _e('Search', 'multi-vender-book-store'); ?></button>
						</div>
					</form>
				</div>
			</div>

			<!-- Vendors Grid -->
			<div class="mvbs-vendors-container">
				<?php if (empty($vendors)): ?>
					<div class="mvbs-empty-state">
						<div class="mvbs-empty-icon">🏪</div>
						<h3 class="mvbs-empty-title"><?php _e('No Vendors Found', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-empty-description"><?php _e('No vendors match your current filters. Try adjusting your search or create a test vendor.', 'multi-vender-book-store'); ?></p>
					</div>
				<?php else: ?>
					<div class="mvbs-vendors-grid">
						<?php foreach ($vendors as $vendor): ?>
							<?php
							// Get additional vendor data
							$product_count = Multi_Vender_Book_Store_Database::get_vendor_product_count($vendor->id);
							$user_data = get_userdata($vendor->user_id);
							$avatar_url = get_avatar_url($vendor->user_id, array('size' => 80));

							// Fix empty status - set to pending if empty
							if (empty($vendor->status)) {
								$vendor->status = 'pending';
								// Update in database
								Multi_Vender_Book_Store_Database::update_vendor($vendor->id, array('status' => 'pending'));
							}

							// Determine status info
							$status_info = array(
								'pending' => array('icon' => '⏳', 'class' => 'pending', 'label' => __('Pending Approval', 'multi-vender-book-store')),
								'approved' => array('icon' => '✅', 'class' => 'approved', 'label' => __('Approved', 'multi-vender-book-store')),
								'suspended' => array('icon' => '⛔', 'class' => 'suspended', 'label' => __('Suspended', 'multi-vender-book-store')),
								'rejected' => array('icon' => '❌', 'class' => 'rejected', 'label' => __('Rejected', 'multi-vender-book-store'))
							);

							$current_status = $status_info[$vendor->status] ?? $status_info['pending'];
							?>
							<div class="mvbs-vendor-card mvbs-status-<?php echo esc_attr($vendor->status); ?>">
								<!-- Card Header -->
								<div class="mvbs-vendor-header">
									<div class="mvbs-vendor-avatar">
										<img src="<?php echo esc_url($avatar_url); ?>" alt="<?php echo esc_attr($vendor->business_name); ?>" class="mvbs-avatar-img">
									</div>
									<div class="mvbs-vendor-info">
										<h3 class="mvbs-vendor-name"><?php echo esc_html($vendor->business_name ?: $user_data->display_name); ?></h3>
										<p class="mvbs-vendor-email"><?php echo esc_html($vendor->business_email ?: $user_data->user_email); ?></p>
										<div class="mvbs-vendor-status mvbs-status-<?php echo esc_attr($vendor->status); ?>">
											<span class="mvbs-status-icon"><?php echo $current_status['icon']; ?></span>
											<span class="mvbs-status-text"><?php echo $current_status['label']; ?></span>
										</div>
									</div>
								</div>

								<!-- Card Stats -->
								<div class="mvbs-vendor-stats">
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">📦</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo $product_count; ?></span>
											<span class="mvbs-stat-label"><?php _e('Products', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">⭐</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo number_format($vendor->average_rating ?? 0, 1); ?></span>
											<span class="mvbs-stat-label"><?php _e('Rating', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
									<div class="mvbs-stat-item">
										<span class="mvbs-stat-icon">📅</span>
										<div class="mvbs-stat-content">
											<span class="mvbs-stat-number"><?php echo date('M j', strtotime($vendor->created_at)); ?></span>
											<span class="mvbs-stat-label"><?php _e('Joined', 'multi-vender-book-store'); ?></span>
										</div>
									</div>
								</div>

								<!-- Card Details -->
								<?php if ($vendor->business_phone || $vendor->business_address): ?>
									<div class="mvbs-vendor-details">
										<?php if ($vendor->business_phone): ?>
											<div class="mvbs-detail-item">
												<span class="mvbs-detail-icon">📞</span>
												<span class="mvbs-detail-text"><?php echo esc_html($vendor->business_phone); ?></span>
											</div>
										<?php endif; ?>
										<?php if ($vendor->business_address): ?>
											<div class="mvbs-detail-item">
												<span class="mvbs-detail-icon">📍</span>
												<span class="mvbs-detail-text"><?php echo esc_html(wp_trim_words($vendor->business_address, 8)); ?></span>
											</div>
										<?php endif; ?>
									</div>
								<?php endif; ?>

								<!-- Card Actions -->
								<div class="mvbs-vendor-actions">
									<?php
									$dashboard_url = add_query_arg(array(
										'mvbs_admin_view' => 'vendor_dashboard',
										'vendor_id' => $vendor->id
									), get_permalink(get_option('mvbs_vendor_dashboard_page_id')));
									?>
									<a href="<?php echo esc_url($dashboard_url); ?>" class="mvbs-action-btn mvbs-btn-view" target="_blank">
										<span class="mvbs-btn-icon">👁️</span>
										<?php _e('View Dashboard', 'multi-vender-book-store'); ?>
									</a>

									<div class="mvbs-action-buttons">
										<?php if ($vendor->status === 'pending' || empty($vendor->status)): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Approve this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">✅</span>
													<?php _e('Approve', 'multi-vender-book-store'); ?>
												</button>
											</form>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="reject">
												<button type="submit" class="mvbs-action-btn mvbs-btn-reject" onclick="return confirm('<?php _e('Reject this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">❌</span>
													<?php _e('Reject', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($vendor->status === 'approved'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="suspend">
												<button type="submit" class="mvbs-action-btn mvbs-btn-suspend" onclick="return confirm('<?php _e('Suspend this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">⛔</span>
													<?php _e('Suspend', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php elseif ($vendor->status === 'suspended'): ?>
											<form method="post" class="mvbs-action-form">
												<input type="hidden" name="vendor_id" value="<?php echo $vendor->id; ?>">
												<input type="hidden" name="action" value="approve">
												<button type="submit" class="mvbs-action-btn mvbs-btn-approve" onclick="return confirm('<?php _e('Reactivate this vendor?', 'multi-vender-book-store'); ?>')">
													<span class="mvbs-btn-icon">🔄</span>
													<?php _e('Reactivate', 'multi-vender-book-store'); ?>
												</button>
											</form>
										<?php endif; ?>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<!-- Modern CSS Styles -->
		<style>
		.mvbs-vendor-management {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		}

		/* Page Header */
		.mvbs-page-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 30px;
			border-radius: 12px;
			margin-bottom: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
		}

		.mvbs-page-title {
			font-size: 28px;
			font-weight: 700;
			margin: 0;
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.mvbs-title-icon {
			font-size: 32px;
		}

		.mvbs-page-subtitle {
			margin: 8px 0 0 0;
			opacity: 0.9;
			font-size: 16px;
		}

		.mvbs-btn {
			padding: 12px 20px;
			border: none;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;
			font-size: 14px;
		}

		.mvbs-btn-secondary {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 2px solid rgba(255, 255, 255, 0.3);
		}

		.mvbs-btn-secondary:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-2px);
		}

		/* Filter Tabs */
		.mvbs-filter-tabs-container {
			background: white;
			border-radius: 12px;
			padding: 20px;
			margin-bottom: 30px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-filter-tabs {
			display: flex;
			gap: 8px;
			margin-bottom: 20px;
			border-bottom: 2px solid #f1f5f9;
			padding-bottom: 20px;
		}

		.mvbs-filter-tab {
			padding: 12px 20px;
			border-radius: 8px;
			text-decoration: none;
			color: #64748b;
			font-weight: 600;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 8px;
			border: 2px solid transparent;
		}

		.mvbs-filter-tab:hover {
			background: #f8fafc;
			color: #334155;
			transform: translateY(-2px);
		}

		.mvbs-filter-tab.active {
			background: #3b82f6;
			color: white;
			box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
		}

		.mvbs-tab-count {
			background: rgba(255, 255, 255, 0.2);
			padding: 4px 8px;
			border-radius: 12px;
			font-size: 12px;
			font-weight: 700;
		}

		.mvbs-filter-tab.active .mvbs-tab-count {
			background: rgba(255, 255, 255, 0.3);
		}

		/* Search */
		.mvbs-search-container {
			max-width: 500px;
		}

		.mvbs-search-input-group {
			display: flex;
			align-items: center;
			background: #f8fafc;
			border: 2px solid #e2e8f0;
			border-radius: 12px;
			padding: 4px;
			transition: all 0.3s ease;
		}

		.mvbs-search-input-group:focus-within {
			border-color: #3b82f6;
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		}

		.mvbs-search-icon {
			padding: 0 12px;
			color: #64748b;
		}

		.mvbs-search-input {
			flex: 1;
			border: none;
			background: transparent;
			padding: 12px 8px;
			font-size: 14px;
			outline: none;
		}

		.mvbs-search-btn {
			background: #3b82f6;
			color: white;
			border: none;
			padding: 12px 20px;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.mvbs-search-btn:hover {
			background: #2563eb;
			transform: translateY(-1px);
		}

		/* Empty State */
		.mvbs-empty-state {
			text-align: center;
			padding: 80px 40px;
			background: white;
			border-radius: 12px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
		}

		.mvbs-empty-icon {
			font-size: 64px;
			margin-bottom: 20px;
		}

		.mvbs-empty-title {
			font-size: 24px;
			color: #334155;
			margin-bottom: 12px;
		}

		.mvbs-empty-description {
			color: #64748b;
			font-size: 16px;
			max-width: 400px;
			margin: 0 auto;
		}

		/* Vendors Grid */
		.mvbs-vendors-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
			gap: 24px;
		}

		.mvbs-vendor-card {
			background: white;
			border-radius: 16px;
			padding: 24px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			border: 2px solid transparent;
		}

		.mvbs-vendor-card:hover {
			transform: translateY(-4px);
			box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		}

		.mvbs-vendor-card.mvbs-status-pending {
			border-color: #fbbf24;
		}

		.mvbs-vendor-card.mvbs-status-approved {
			border-color: #10b981;
		}

		.mvbs-vendor-card.mvbs-status-suspended {
			border-color: #ef4444;
		}

		.mvbs-vendor-card.mvbs-status-rejected {
			border-color: #6b7280;
		}

		/* Vendor Header */
		.mvbs-vendor-header {
			display: flex;
			align-items: center;
			gap: 16px;
			margin-bottom: 20px;
		}

		.mvbs-vendor-avatar {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			overflow: hidden;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}

		.mvbs-avatar-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.mvbs-vendor-name {
			font-size: 18px;
			font-weight: 700;
			color: #1e293b;
			margin: 0 0 4px 0;
		}

		.mvbs-vendor-email {
			color: #64748b;
			font-size: 14px;
			margin: 0 0 8px 0;
		}

		.mvbs-vendor-status {
			display: inline-flex;
			align-items: center;
			gap: 6px;
			padding: 6px 12px;
			border-radius: 20px;
			font-size: 12px;
			font-weight: 600;
		}

		.mvbs-status-pending {
			background: #fef3c7;
			color: #92400e;
		}

		.mvbs-status-approved {
			background: #d1fae5;
			color: #065f46;
		}

		.mvbs-status-suspended {
			background: #fee2e2;
			color: #991b1b;
		}

		.mvbs-status-rejected {
			background: #f3f4f6;
			color: #374151;
		}

		/* Vendor Stats */
		.mvbs-vendor-stats {
			display: flex;
			justify-content: space-between;
			margin-bottom: 20px;
			padding: 16px;
			background: #f8fafc;
			border-radius: 12px;
		}

		.mvbs-stat-item {
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.mvbs-stat-icon {
			font-size: 20px;
		}

		.mvbs-stat-content {
			display: flex;
			flex-direction: column;
		}

		.mvbs-stat-number {
			font-size: 16px;
			font-weight: 700;
			color: #1e293b;
		}

		.mvbs-stat-label {
			font-size: 11px;
			color: #64748b;
			text-transform: uppercase;
			font-weight: 600;
		}

		/* Vendor Details */
		.mvbs-vendor-details {
			margin-bottom: 20px;
		}

		.mvbs-detail-item {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 8px;
			color: #64748b;
			font-size: 14px;
		}

		.mvbs-detail-icon {
			font-size: 16px;
		}

		/* Vendor Actions */
		.mvbs-vendor-actions {
			border-top: 1px solid #e2e8f0;
			padding-top: 20px;
		}

		.mvbs-action-btn {
			display: inline-flex;
			align-items: center;
			gap: 6px;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 13px;
			font-weight: 600;
			text-decoration: none;
			border: none;
			cursor: pointer;
			transition: all 0.3s ease;
			margin-right: 8px;
			margin-bottom: 8px;
		}

		.mvbs-btn-view {
			background: #f1f5f9;
			color: #475569;
		}

		.mvbs-btn-view:hover {
			background: #e2e8f0;
			transform: translateY(-1px);
		}

		.mvbs-action-buttons {
			display: flex;
			gap: 8px;
			flex-wrap: wrap;
			margin-top: 12px;
		}

		.mvbs-action-form {
			display: inline;
		}

		.mvbs-btn-approve {
			background: #10b981;
			color: white;
		}

		.mvbs-btn-approve:hover {
			background: #059669;
			transform: translateY(-1px);
		}

		.mvbs-btn-reject {
			background: #ef4444;
			color: white;
		}

		.mvbs-btn-reject:hover {
			background: #dc2626;
			transform: translateY(-1px);
		}

		.mvbs-btn-suspend {
			background: #f59e0b;
			color: white;
		}

		.mvbs-btn-suspend:hover {
			background: #d97706;
			transform: translateY(-1px);
		}

		/* Responsive Design */
		@media (max-width: 768px) {
			.mvbs-vendors-grid {
				grid-template-columns: 1fr;
			}

			.mvbs-page-header {
				flex-direction: column;
				text-align: center;
				gap: 20px;
			}

			.mvbs-filter-tabs {
				flex-wrap: wrap;
			}

			.mvbs-vendor-stats {
				flex-direction: column;
				gap: 12px;
			}
		}
		</style>
		<?php
	}

	/**
	 * Display products management page.
	 *
	 * @since    1.0.0
	 */
	public function display_products_page() {
		// Handle product approval
		if (isset($_POST['approve_product']) && isset($_POST['product_id'])) {
			$product_id = intval($_POST['product_id']);
			if (Multi_Vender_Book_Store_Product::approve_product($product_id)) {
				echo '<div class="notice notice-success"><p>' . __('Product approved successfully.', 'multi-vender-book-store') . '</p></div>';
			}
		}

		// Get pending products
		global $wpdb;
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$pending_products = $wpdb->get_results(
			"SELECT vp.*, p.post_title, p.post_content, v.business_name
			FROM $vendor_products_table vp
			JOIN {$wpdb->posts} p ON vp.product_id = p.ID
			JOIN {$wpdb->prefix}mvbs_vendors v ON vp.vendor_id = v.id
			WHERE vp.status = 'pending'
			ORDER BY vp.created_at DESC
			LIMIT 50"
		);

		echo '<div class="wrap">';
		echo '<h1>' . __('Vendor Products', 'multi-vender-book-store') . '</h1>';

		if (empty($pending_products)) {
			echo '<p>' . __('No pending products for review.', 'multi-vender-book-store') . '</p>';
		} else {
			echo '<table class="wp-list-table widefat fixed striped">';
			echo '<thead>';
			echo '<tr>';
			echo '<th>' . __('Product', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Vendor', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Price', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Condition', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Submitted', 'multi-vender-book-store') . '</th>';
			echo '<th>' . __('Actions', 'multi-vender-book-store') . '</th>';
			echo '</tr>';
			echo '</thead>';
			echo '<tbody>';

			foreach ($pending_products as $product) {
				$price = get_post_meta($product->product_id, '_regular_price', true);
				$condition = get_post_meta($product->product_id, 'book_condition', true);

				echo '<tr>';
				echo '<td>';
				echo '<strong>' . esc_html($product->post_title) . '</strong><br>';
				echo '<small>' . wp_trim_words($product->post_content, 15) . '</small>';
				echo '</td>';
				echo '<td>' . esc_html($product->business_name) . '</td>';
				echo '<td>$' . number_format(floatval($price), 2) . '</td>';
				echo '<td>' . ucfirst(str_replace('_', ' ', $condition)) . '</td>';
				echo '<td>' . date('M j, Y', strtotime($product->created_at)) . '</td>';
				echo '<td>';
				echo '<form method="post" style="display:inline;">';
				echo '<input type="hidden" name="product_id" value="' . $product->product_id . '">';
				echo '<input type="submit" name="approve_product" value="' . __('Approve', 'multi-vender-book-store') . '" class="button button-primary button-small">';
				echo '</form> ';
				echo '<a href="' . get_edit_post_link($product->product_id) . '" class="button button-small">' . __('Edit', 'multi-vender-book-store') . '</a>';
				echo '</td>';
				echo '</tr>';
			}

			echo '</tbody>';
			echo '</table>';
		}

		echo '</div>';
	}

	/**
	 * Display commissions page.
	 *
	 * @since    1.0.0
	 */
	public function display_commissions_page() {
		echo '<div class="wrap">';
		echo '<h1>' . __('Commission Management', 'multi-vender-book-store') . '</h1>';
		echo '<p>' . __('Track and manage vendor commissions.', 'multi-vender-book-store') . '</p>';
		echo '</div>';
	}

	/**
	 * Display settings page.
	 *
	 * @since    1.0.0
	 */
	public function display_settings_page() {
		if (isset($_POST['submit'])) {
			update_option('mvbs_default_commission_rate', sanitize_text_field($_POST['mvbs_default_commission_rate']));
			update_option('mvbs_buyer_markup_rate', sanitize_text_field($_POST['mvbs_buyer_markup_rate']));
			update_option('mvbs_auto_approve_vendors', isset($_POST['mvbs_auto_approve_vendors']) ? 'yes' : 'no');
			update_option('mvbs_auto_approve_products', isset($_POST['mvbs_auto_approve_products']) ? 'yes' : 'no');
			update_option('mvbs_minimum_payout_amount', sanitize_text_field($_POST['mvbs_minimum_payout_amount']));

			echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'multi-vender-book-store') . '</p></div>';
		}

		$commission_rate = get_option('mvbs_default_commission_rate', '10.00');
		$markup_rate = get_option('mvbs_buyer_markup_rate', '5.00');
		$auto_approve_vendors = get_option('mvbs_auto_approve_vendors', 'no');
		$auto_approve_products = get_option('mvbs_auto_approve_products', 'no');
		$minimum_payout = get_option('mvbs_minimum_payout_amount', '50.00');

		echo '<div class="wrap">';
		echo '<h1>' . __('Multi-Vendor Store Settings', 'multi-vender-book-store') . '</h1>';
		echo '<form method="post" action="">';
		echo '<table class="form-table">';

		echo '<tr>';
		echo '<th scope="row">' . __('Default Commission Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_default_commission_rate" value="' . esc_attr($commission_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Buyer Markup Rate (%)', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_buyer_markup_rate" value="' . esc_attr($markup_rate) . '" /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Vendors', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_vendors" ' . checked($auto_approve_vendors, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Auto-approve Products', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="checkbox" name="mvbs_auto_approve_products" ' . checked($auto_approve_products, 'yes', false) . ' /></td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">' . __('Minimum Payout Amount', 'multi-vender-book-store') . '</th>';
		echo '<td><input type="number" step="0.01" name="mvbs_minimum_payout_amount" value="' . esc_attr($minimum_payout) . '" /></td>';
		echo '</tr>';

		echo '</table>';
		echo '<p class="submit"><input type="submit" name="submit" class="button-primary" value="' . __('Save Settings', 'multi-vender-book-store') . '" /></p>';
		echo '</form>';
		echo '</div>';
	}

	/**
	 * Display messages management page.
	 *
	 * @since    1.0.0
	 */
	public function display_messages_page() {
		// Handle message actions
		if (isset($_POST['action']) && isset($_POST['message_id'])) {
			$message_id = intval($_POST['message_id']);
			$action = sanitize_text_field($_POST['action']);

			switch ($action) {
				case 'mark_read':
					Multi_Vender_Book_Store_Message::admin_mark_as_read($message_id);
					echo '<div class="notice notice-success is-dismissible"><p>' . __('Message marked as read.', 'multi-vender-book-store') . '</p></div>';
					break;
				case 'delete':
					Multi_Vender_Book_Store_Message::admin_delete_message($message_id);
					echo '<div class="notice notice-success is-dismissible"><p>' . __('Message deleted.', 'multi-vender-book-store') . '</p></div>';
					break;
			}
		}

		// Get messages
		$messages = Multi_Vender_Book_Store_Message::get_admin_messages(array('limit' => 50));

		?>
		<div class="wrap">
			<h1><?php _e('Messages', 'multi-vender-book-store'); ?></h1>
			<p><?php _e('Manage communication between vendors and customers', 'multi-vender-book-store'); ?></p>

			<?php if (empty($messages)): ?>
				<div style="text-align: center; padding: 40px; background: white; border: 1px solid #ccd0d4;">
					<h3><?php _e('No Messages Found', 'multi-vender-book-store'); ?></h3>
					<p><?php _e('No messages have been sent yet.', 'multi-vender-book-store'); ?></p>
				</div>
			<?php else: ?>
				<table class="wp-list-table widefat fixed striped">
					<thead>
						<tr>
							<th><?php _e('From', 'multi-vender-book-store'); ?></th>
							<th><?php _e('To', 'multi-vender-book-store'); ?></th>
							<th><?php _e('Subject', 'multi-vender-book-store'); ?></th>
							<th><?php _e('Date', 'multi-vender-book-store'); ?></th>
							<th><?php _e('Status', 'multi-vender-book-store'); ?></th>
							<th><?php _e('Actions', 'multi-vender-book-store'); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ($messages as $message): ?>
							<tr style="<?php echo $message->is_read ? '' : 'font-weight: bold; background: #f0f8ff;'; ?>">
								<td>
									<?php
									$from_user = get_user_by('ID', $message->sender_id);
									echo $from_user ? esc_html($from_user->display_name) : __('Unknown', 'multi-vender-book-store');
									?>
								</td>
								<td>
									<?php
									$to_user = get_user_by('ID', $message->recipient_id);
									echo $to_user ? esc_html($to_user->display_name) : __('Unknown', 'multi-vender-book-store');
									?>
								</td>
								<td>
									<strong><?php echo esc_html($message->subject); ?></strong>
									<br><small><?php echo esc_html(wp_trim_words($message->message, 15)); ?></small>
								</td>
								<td><?php echo date('M j, Y g:i A', strtotime($message->created_at)); ?></td>
								<td>
									<span style="padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: 600; text-transform: uppercase; <?php echo $message->is_read ? 'background: #d4edda; color: #155724;' : 'background: #fff3cd; color: #856404;'; ?>">
										<?php echo $message->is_read ? __('Read', 'multi-vender-book-store') : __('Unread', 'multi-vender-book-store'); ?>
									</span>
								</td>
								<td>
									<?php if (!$message->is_read): ?>
										<form method="post" style="display: inline;">
											<input type="hidden" name="message_id" value="<?php echo $message->id; ?>">
											<input type="hidden" name="action" value="mark_read">
											<button type="submit" class="button button-small"><?php _e('Mark Read', 'multi-vender-book-store'); ?></button>
										</form>
									<?php endif; ?>
									<form method="post" style="display: inline; margin-left: 5px;">
										<input type="hidden" name="message_id" value="<?php echo $message->id; ?>">
										<input type="hidden" name="action" value="delete">
										<button type="submit" class="button button-small" onclick="return confirm('<?php _e('Delete this message?', 'multi-vender-book-store'); ?>')" style="color: #a00;">
											<?php _e('Delete', 'multi-vender-book-store'); ?>
										</button>
									</form>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			<?php endif; ?>
		</div>
		<?php
	}

	/**
	 * Process completed WooCommerce order.
	 *
	 * @since    1.0.0
	 * @param    int  $order_id  Order ID.
	 */
	public function process_completed_order($order_id) {
		Multi_Vender_Book_Store_Order::process_order($order_id);
	}

}
