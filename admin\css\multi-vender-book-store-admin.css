/**
 * Modern Admin Interface for Multi-Vendor Book Store
 */

/* Admin Dashboard */
.mvbs-admin-dashboard {
    background: #f8fafc;
    margin: 0 -20px;
    padding: 20px;
    min-height: calc(100vh - 32px);
}

.mvbs-admin-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.mvbs-admin-header h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
}

.mvbs-admin-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

/* Stats Grid */
.mvbs-admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.mvbs-admin-stat-card {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.3s ease;
    border-left: 4px solid transparent;
}

.mvbs-admin-stat-card:hover {
    transform: translateY(-4px);
}

.mvbs-stat-primary {
    border-left-color: #667eea;
}

.mvbs-stat-success {
    border-left-color: #10b981;
}

.mvbs-stat-info {
    border-left-color: #3b82f6;
}

.mvbs-stat-warning {
    border-left-color: #f59e0b;
}

.mvbs-stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.mvbs-stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.mvbs-stat-content p {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-stat-badge {
    background: #fbbf24;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.mvbs-stat-detail {
    color: #6b7280;
    font-size: 12px;
}

/* Content Grid */
.mvbs-admin-content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

.mvbs-admin-panel {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.mvbs-panel-header {
    padding: 24px;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mvbs-panel-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
}

.mvbs-panel-content {
    padding: 24px;
}

/* Vendor Performance */
.mvbs-vendor-performance h4 {
    margin: 0 0 16px 0;
    color: #374151;
}

.mvbs-vendor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.mvbs-vendor-item:last-child {
    border-bottom: none;
}

.mvbs-vendor-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.mvbs-vendor-sales {
    color: #10b981;
    font-weight: 600;
}

.mvbs-vendor-meta {
    color: #6b7280;
    font-size: 12px;
}

/* Quick Actions */
.mvbs-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.mvbs-quick-action {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.mvbs-quick-action:hover {
    background: #e2e8f0;
    border-color: #667eea;
    transform: translateY(-2px);
    text-decoration: none;
}

.mvbs-action-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mvbs-action-content h4 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 16px;
}

.mvbs-action-content p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

/* Admin Buttons */
.mvbs-admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mvbs-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.mvbs-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

.mvbs-btn-secondary {
    background: #f8fafc;
    color: #374151;
    border: 1px solid #e5e7eb;
}

.mvbs-btn-secondary:hover {
    background: #e5e7eb;
    color: #1f2937;
}

.mvbs-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.mvbs-btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    color: white;
}

.mvbs-btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.mvbs-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    color: white;
}

.mvbs-btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.mvbs-btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    color: white;
}

.mvbs-btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* Empty State */
.mvbs-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

.mvbs-empty-state p {
    margin: 0;
    font-style: italic;
}

/* Vendor Management */
.mvbs-admin-vendors {
    background: #f8fafc;
    margin: 0 -20px;
    padding: 20px;
    min-height: calc(100vh - 32px);
}

.mvbs-admin-filters {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.mvbs-filter-tabs {
    display: flex;
    gap: 8px;
}

.mvbs-filter-tab {
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.mvbs-filter-tab:hover {
    background: #f1f5f9;
    color: #374151;
    text-decoration: none;
}

.mvbs-filter-tab.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.mvbs-search-box {
    display: flex;
    gap: 8px;
}

.mvbs-search-box input[type="search"] {
    padding: 8px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    min-width: 200px;
}

.mvbs-search-box input[type="search"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Vendors Grid */
.mvbs-vendors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
}

.mvbs-vendor-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid transparent;
}

.mvbs-vendor-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-vendor-card.mvbs-status-pending {
    border-left-color: #f59e0b;
}

.mvbs-vendor-card.mvbs-status-approved {
    border-left-color: #10b981;
}

.mvbs-vendor-card.mvbs-status-suspended {
    border-left-color: #ef4444;
}

.mvbs-vendor-card.mvbs-status-rejected {
    border-left-color: #6b7280;
}

.mvbs-vendor-header {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.mvbs-vendor-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.mvbs-vendor-info h3 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.mvbs-vendor-email {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 14px;
}

.mvbs-vendor-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-vendor-status.mvbs-status-pending {
    background: #fef3c7;
    color: #92400e;
}

.mvbs-vendor-status.mvbs-status-approved {
    background: #d1fae5;
    color: #065f46;
}

.mvbs-vendor-status.mvbs-status-suspended {
    background: #fee2e2;
    color: #991b1b;
}

.mvbs-vendor-status.mvbs-status-rejected {
    background: #f3f4f6;
    color: #374151;
}

.mvbs-vendor-stats {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    background: #f8fafc;
    border-bottom: 1px solid #f1f5f9;
}

.mvbs-vendor-stat {
    text-align: center;
}

.mvbs-vendor-stat .mvbs-stat-number {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2px;
}

.mvbs-vendor-stat .mvbs-stat-label {
    font-size: 11px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-vendor-details {
    padding: 16px 24px;
    border-bottom: 1px solid #f1f5f9;
}

.mvbs-vendor-details p {
    margin: 8px 0;
    font-size: 14px;
    color: #374151;
}

.mvbs-vendor-actions {
    padding: 16px 24px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Admin Empty State */
.mvbs-admin-empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.mvbs-admin-empty-state h3 {
    color: #374151;
    margin-bottom: 12px;
}

.mvbs-admin-empty-state p {
    color: #6b7280;
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mvbs-admin-content-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-admin-stats-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-vendors-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-admin-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .mvbs-filter-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .mvbs-vendor-stats {
        flex-direction: column;
        gap: 12px;
    }

    .mvbs-vendor-actions {
        flex-direction: column;
    }
}