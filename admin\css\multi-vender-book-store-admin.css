/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */

/* Admin Status Badges */
.mvbs-status-pending {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.mvbs-status-approved {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.mvbs-status-rejected {
    background: #f8d7da;
    color: #721c24;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.mvbs-status-suspended {
    background: #e2e3e5;
    color: #383d41;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

/* Admin Tables */
.wp-list-table .button-small {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.4;
    margin-right: 4px;
}

.wp-list-table td small {
    color: #666;
    font-style: italic;
}

/* Admin Dashboard Cards */
.mvbs-admin-card {
    background: white;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.mvbs-admin-card h3 {
    margin-top: 0;
    color: #23282d;
}

.mvbs-admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.mvbs-admin-stat {
    text-align: center;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;
}

.mvbs-admin-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.mvbs-admin-stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}