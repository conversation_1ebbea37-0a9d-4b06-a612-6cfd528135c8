<?php

/**
 * Fired during plugin activation
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Activator {

	/**
	 * Plugin activation handler.
	 *
	 * Creates database tables, user roles, and sets up initial configuration
	 * for the multi-vendor book store system.
	 *
	 * @since    1.0.0
	 */
	public static function activate() {
		self::create_database_tables();
		self::create_user_roles();
		self::set_default_options();
		self::create_pages();

		// Flush rewrite rules to ensure custom endpoints work
		flush_rewrite_rules();
	}

	/**
	 * Create custom database tables for the multi-vendor system.
	 *
	 * @since    1.0.0
	 */
	private static function create_database_tables() {
		global $wpdb;

		$charset_collate = $wpdb->get_charset_collate();

		// Vendors table
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';
		$vendors_sql = "CREATE TABLE $vendors_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			user_id bigint(20) NOT NULL,
			business_name varchar(255) NOT NULL,
			business_email varchar(100) NOT NULL,
			business_phone varchar(20),
			business_address text,
			tax_id varchar(50),
			bank_account varchar(100),
			commission_rate decimal(5,2) DEFAULT 10.00,
			status enum('pending','approved','suspended','rejected') DEFAULT 'pending',
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY user_id (user_id),
			KEY status (status)
		) $charset_collate;";

		// Vendor products relationship table
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$vendor_products_sql = "CREATE TABLE $vendor_products_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			vendor_id bigint(20) NOT NULL,
			product_id bigint(20) NOT NULL,
			status enum('draft','pending','approved','rejected') DEFAULT 'draft',
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY vendor_product (vendor_id, product_id),
			KEY vendor_id (vendor_id),
			KEY product_id (product_id),
			KEY status (status)
		) $charset_collate;";

		// Commissions table
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$commissions_sql = "CREATE TABLE $commissions_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			vendor_id bigint(20) NOT NULL,
			order_id bigint(20) NOT NULL,
			product_id bigint(20) NOT NULL,
			order_item_id bigint(20),
			gross_amount decimal(10,2) NOT NULL,
			commission_rate decimal(5,2) NOT NULL,
			commission_amount decimal(10,2) NOT NULL,
			vendor_amount decimal(10,2) NOT NULL,
			status enum('pending','paid','cancelled') DEFAULT 'pending',
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY vendor_id (vendor_id),
			KEY order_id (order_id),
			KEY status (status)
		) $charset_collate;";

		// Messages table
		$messages_table = $wpdb->prefix . 'mvbs_messages';
		$messages_sql = "CREATE TABLE $messages_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			sender_id bigint(20) NOT NULL,
			recipient_id bigint(20) NOT NULL,
			product_id bigint(20),
			order_id bigint(20),
			subject varchar(255),
			message text NOT NULL,
			is_read tinyint(1) DEFAULT 0,
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY sender_id (sender_id),
			KEY recipient_id (recipient_id),
			KEY product_id (product_id),
			KEY order_id (order_id),
			KEY is_read (is_read)
		) $charset_collate;";

		// Payment requests table
		$payment_requests_table = $wpdb->prefix . 'mvbs_payment_requests';
		$payment_requests_sql = "CREATE TABLE $payment_requests_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			vendor_id bigint(20) NOT NULL,
			amount decimal(10,2) NOT NULL,
			commission_ids text NOT NULL,
			status enum('pending','processing','paid','rejected') DEFAULT 'pending',
			requested_at datetime DEFAULT CURRENT_TIMESTAMP,
			processed_at datetime NULL,
			notes text,
			PRIMARY KEY (id),
			KEY vendor_id (vendor_id),
			KEY status (status)
		) $charset_collate;";

		// API logs table
		$api_logs_table = $wpdb->prefix . 'mvbs_api_logs';
		$api_logs_sql = "CREATE TABLE $api_logs_table (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			api_name varchar(50) NOT NULL,
			endpoint varchar(255) NOT NULL,
			method varchar(10) NOT NULL,
			request_data text,
			response_data text,
			status_code int(3),
			execution_time decimal(8,3),
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY api_name (api_name),
			KEY created_at (created_at)
		) $charset_collate;";

		require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

		dbDelta($vendors_sql);
		dbDelta($vendor_products_sql);
		dbDelta($commissions_sql);
		dbDelta($messages_sql);
		dbDelta($payment_requests_sql);
		dbDelta($api_logs_sql);
	}

	/**
	 * Create custom user roles for the multi-vendor system.
	 *
	 * @since    1.0.0
	 */
	private static function create_user_roles() {
		// Create vendor role with specific capabilities
		add_role(
			'vendor',
			__('Vendor', 'multi-vender-book-store'),
			array(
				'read' => true,
				'upload_files' => true,
				'edit_posts' => false,
				'edit_others_posts' => false,
				'create_posts' => false,
				'manage_categories' => false,
				'publish_posts' => false,
				// Custom vendor capabilities
				'manage_vendor_products' => true,
				'edit_vendor_products' => true,
				'delete_vendor_products' => true,
				'view_vendor_dashboard' => true,
				'manage_vendor_orders' => true,
				'send_vendor_messages' => true,
				'request_vendor_payments' => true,
			)
		);

		// Add capabilities to administrator role
		$admin_role = get_role('administrator');
		if ($admin_role) {
			$admin_role->add_cap('manage_vendors');
			$admin_role->add_cap('approve_vendor_products');
			$admin_role->add_cap('manage_vendor_commissions');
			$admin_role->add_cap('process_vendor_payments');
			$admin_role->add_cap('view_vendor_analytics');
		}
	}

	/**
	 * Set default plugin options.
	 *
	 * @since    1.0.0
	 */
	private static function set_default_options() {
		// Commission settings
		add_option('mvbs_default_commission_rate', '10.00');
		add_option('mvbs_buyer_markup_rate', '5.00');
		add_option('mvbs_auto_approve_vendors', 'no');
		add_option('mvbs_auto_approve_products', 'no');

		// Email settings
		add_option('mvbs_vendor_registration_email', 'yes');
		add_option('mvbs_product_approval_email', 'yes');
		add_option('mvbs_order_notification_email', 'yes');
		add_option('mvbs_payment_notification_email', 'yes');

		// API settings
		add_option('mvbs_vitalsource_api_enabled', 'no');
		add_option('mvbs_dropp_api_enabled', 'no');

		// General settings
		add_option('mvbs_enable_messaging', 'yes');
		add_option('mvbs_enable_pickup_scheduling', 'yes');
		add_option('mvbs_require_vendor_approval', 'yes');
		add_option('mvbs_minimum_payout_amount', '50.00');
	}

	/**
	 * Create necessary pages for the multi-vendor system.
	 *
	 * @since    1.0.0
	 */
	private static function create_pages() {
		// Vendor registration page
		$vendor_registration_page = array(
			'post_title'    => __('Vendor Registration', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_vendor_registration]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'vendor-registration'
		);
		$registration_page_id = wp_insert_post($vendor_registration_page);
		add_option('mvbs_vendor_registration_page_id', $registration_page_id);

		// Vendor login page
		$vendor_login_page = array(
			'post_title'    => __('Vendor Login', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_vendor_login]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'vendor-login'
		);
		$login_page_id = wp_insert_post($vendor_login_page);
		add_option('mvbs_vendor_login_page_id', $login_page_id);

		// Vendor dashboard page
		$vendor_dashboard_page = array(
			'post_title'    => __('Vendor Dashboard', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_vendor_dashboard]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'vendor-dashboard'
		);
		$dashboard_page_id = wp_insert_post($vendor_dashboard_page);
		add_option('mvbs_vendor_dashboard_page_id', $dashboard_page_id);

		// Messaging page
		$messaging_page = array(
			'post_title'    => __('Messages', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_messaging]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'messages'
		);
		$messaging_page_id = wp_insert_post($messaging_page);
		add_option('mvbs_messaging_page_id', $messaging_page_id);

		// Add Product page
		$add_product_page = array(
			'post_title'    => __('Add Product', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_add_product]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'add-product'
		);
		$add_product_page_id = wp_insert_post($add_product_page);
		add_option('mvbs_add_product_page_id', $add_product_page_id);

		// Manage Products page
		$manage_products_page = array(
			'post_title'    => __('Manage Products', 'multi-vender-book-store'),
			'post_content'  => '[mvbs_manage_products]',
			'post_status'   => 'publish',
			'post_type'     => 'page',
			'post_name'     => 'manage-products'
		);
		$manage_products_page_id = wp_insert_post($manage_products_page);
		add_option('mvbs_manage_products_page_id', $manage_products_page_id);
	}

}
