<?php

/**
 * Analytics and reporting functionality for the multi-vendor system.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Analytics class.
 *
 * Handles comprehensive analytics, reporting, and insights
 * for the multi-vendor marketplace system.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Analytics {

	/**
	 * Get comprehensive dashboard analytics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Analytics data.
	 */
	public static function get_dashboard_analytics($args = array()) {
		$defaults = array(
			'period' => '30', // days
			'vendor_id' => null
		);
		
		$args = wp_parse_args($args, $defaults);
		
		return array(
			'overview' => self::get_overview_stats($args),
			'sales' => self::get_sales_analytics($args),
			'vendors' => self::get_vendor_analytics($args),
			'products' => self::get_product_analytics($args),
			'commissions' => self::get_commission_analytics($args),
			'trends' => self::get_trend_data($args)
		);
	}

	/**
	 * Get overview statistics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Overview stats.
	 */
	public static function get_overview_stats($args = array()) {
		global $wpdb;
		
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';
		$products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		$date_filter = '';
		if ($args['period']) {
			$date_filter = $wpdb->prepare(" AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)", $args['period']);
		}
		
		// Total vendors
		$total_vendors = $wpdb->get_var("SELECT COUNT(*) FROM $vendors_table WHERE status = 'approved'");
		
		// Pending vendors
		$pending_vendors = $wpdb->get_var("SELECT COUNT(*) FROM $vendors_table WHERE status = 'pending'");
		
		// Total products
		$total_products = $wpdb->get_var("SELECT COUNT(*) FROM $products_table WHERE status = 'approved'");
		
		// Pending products
		$pending_products = $wpdb->get_var("SELECT COUNT(*) FROM $products_table WHERE status = 'pending'");
		
		// Total sales
		$total_sales = $wpdb->get_var("SELECT SUM(gross_amount) FROM $commissions_table WHERE 1=1 $date_filter");
		
		// Total commissions
		$total_commissions = $wpdb->get_var("SELECT SUM(commission_amount) FROM $commissions_table WHERE 1=1 $date_filter");
		
		// Total orders
		$total_orders = $wpdb->get_var("SELECT COUNT(DISTINCT order_id) FROM $commissions_table WHERE 1=1 $date_filter");
		
		return array(
			'total_vendors' => intval($total_vendors),
			'pending_vendors' => intval($pending_vendors),
			'total_products' => intval($total_products),
			'pending_products' => intval($pending_products),
			'total_sales' => floatval($total_sales),
			'total_commissions' => floatval($total_commissions),
			'total_orders' => intval($total_orders),
			'average_order_value' => $total_orders > 0 ? floatval($total_sales) / intval($total_orders) : 0
		);
	}

	/**
	 * Get sales analytics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Sales analytics.
	 */
	public static function get_sales_analytics($args = array()) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$period = intval($args['period'] ?? 30);
		
		// Daily sales for the period
		$daily_sales = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				DATE(created_at) as date,
				SUM(gross_amount) as sales,
				SUM(commission_amount) as commissions,
				COUNT(DISTINCT order_id) as orders
			FROM $commissions_table 
			WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY DATE(created_at)
			ORDER BY date ASC",
			$period
		));
		
		// Top selling products
		$top_products = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				c.product_id,
				p.post_title as product_name,
				SUM(c.gross_amount) as total_sales,
				COUNT(*) as units_sold
			FROM $commissions_table c
			JOIN {$wpdb->posts} p ON c.product_id = p.ID
			WHERE c.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY c.product_id
			ORDER BY total_sales DESC
			LIMIT 10",
			$period
		));
		
		// Sales by vendor
		$vendor_sales = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				c.vendor_id,
				v.business_name,
				SUM(c.gross_amount) as total_sales,
				SUM(c.vendor_amount) as vendor_earnings,
				COUNT(DISTINCT c.order_id) as orders
			FROM $commissions_table c
			JOIN {$wpdb->prefix}mvbs_vendors v ON c.vendor_id = v.id
			WHERE c.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY c.vendor_id
			ORDER BY total_sales DESC
			LIMIT 10",
			$period
		));
		
		return array(
			'daily_sales' => $daily_sales,
			'top_products' => $top_products,
			'vendor_sales' => $vendor_sales
		);
	}

	/**
	 * Get vendor analytics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Vendor analytics.
	 */
	public static function get_vendor_analytics($args = array()) {
		global $wpdb;
		
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';
		$products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		// Vendor registration trends
		$registration_trends = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				DATE(created_at) as date,
				COUNT(*) as registrations
			FROM $vendors_table 
			WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY DATE(created_at)
			ORDER BY date ASC",
			intval($args['period'] ?? 30)
		));
		
		// Vendor performance metrics
		$vendor_performance = $wpdb->get_results(
			"SELECT 
				v.id,
				v.business_name,
				v.status,
				v.created_at,
				COUNT(DISTINCT vp.product_id) as total_products,
				COUNT(DISTINCT CASE WHEN vp.status = 'approved' THEN vp.product_id END) as approved_products,
				COALESCE(SUM(c.gross_amount), 0) as total_sales,
				COALESCE(SUM(c.vendor_amount), 0) as total_earnings,
				COUNT(DISTINCT c.order_id) as total_orders
			FROM $vendors_table v
			LEFT JOIN $products_table vp ON v.id = vp.vendor_id
			LEFT JOIN $commissions_table c ON v.id = c.vendor_id
			WHERE v.status = 'approved'
			GROUP BY v.id
			ORDER BY total_sales DESC"
		);
		
		return array(
			'registration_trends' => $registration_trends,
			'vendor_performance' => $vendor_performance
		);
	}

	/**
	 * Get product analytics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Product analytics.
	 */
	public static function get_product_analytics($args = array()) {
		global $wpdb;
		
		$products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		// Product status distribution
		$product_status = $wpdb->get_results(
			"SELECT status, COUNT(*) as count FROM $products_table GROUP BY status"
		);
		
		// Product performance
		$product_performance = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				p.ID as product_id,
				p.post_title as product_name,
				vp.status,
				v.business_name as vendor_name,
				COALESCE(SUM(c.gross_amount), 0) as total_sales,
				COUNT(c.id) as units_sold,
				AVG(c.gross_amount) as avg_price
			FROM {$wpdb->posts} p
			JOIN $products_table vp ON p.ID = vp.product_id
			JOIN {$wpdb->prefix}mvbs_vendors v ON vp.vendor_id = v.id
			LEFT JOIN $commissions_table c ON p.ID = c.product_id AND c.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			WHERE p.post_type = 'product'
			GROUP BY p.ID
			ORDER BY total_sales DESC
			LIMIT 20",
			intval($args['period'] ?? 30)
		));
		
		return array(
			'status_distribution' => $product_status,
			'product_performance' => $product_performance
		);
	}

	/**
	 * Get commission analytics.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Commission analytics.
	 */
	public static function get_commission_analytics($args = array()) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$period = intval($args['period'] ?? 30);
		
		// Commission trends
		$commission_trends = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				DATE(created_at) as date,
				SUM(commission_amount) as total_commissions,
				SUM(vendor_amount) as total_vendor_earnings,
				AVG(commission_rate) as avg_commission_rate
			FROM $commissions_table 
			WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY DATE(created_at)
			ORDER BY date ASC",
			$period
		));
		
		// Commission by status
		$commission_status = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				status,
				COUNT(*) as count,
				SUM(commission_amount) as total_commissions,
				SUM(vendor_amount) as total_vendor_amount
			FROM $commissions_table 
			WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY status",
			$period
		));
		
		return array(
			'commission_trends' => $commission_trends,
			'commission_status' => $commission_status
		);
	}

	/**
	 * Get trend data for charts.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Trend data.
	 */
	public static function get_trend_data($args = array()) {
		global $wpdb;
		
		$period = intval($args['period'] ?? 30);
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';
		
		// Generate date range
		$dates = array();
		for ($i = $period - 1; $i >= 0; $i--) {
			$dates[] = date('Y-m-d', strtotime("-$i days"));
		}
		
		// Get sales data for each date
		$sales_data = array();
		$vendor_data = array();
		
		foreach ($dates as $date) {
			// Sales for this date
			$sales = $wpdb->get_var($wpdb->prepare(
				"SELECT COALESCE(SUM(gross_amount), 0) FROM $commissions_table WHERE DATE(created_at) = %s",
				$date
			));
			
			// New vendors for this date
			$new_vendors = $wpdb->get_var($wpdb->prepare(
				"SELECT COUNT(*) FROM $vendors_table WHERE DATE(created_at) = %s",
				$date
			));
			
			$sales_data[] = array(
				'date' => $date,
				'sales' => floatval($sales)
			);
			
			$vendor_data[] = array(
				'date' => $date,
				'vendors' => intval($new_vendors)
			);
		}
		
		return array(
			'sales_trend' => $sales_data,
			'vendor_trend' => $vendor_data
		);
	}

	/**
	 * Get vendor-specific analytics.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    array  $args       Query arguments.
	 * @return   array              Vendor analytics.
	 */
	public static function get_vendor_specific_analytics($vendor_id, $args = array()) {
		$period = intval($args['period'] ?? 30);
		
		return array(
			'overview' => Multi_Vender_Book_Store_Database::get_vendor_stats($vendor_id),
			'sales_trend' => self::get_vendor_sales_trend($vendor_id, $period),
			'product_performance' => self::get_vendor_product_performance($vendor_id, $period),
			'order_analytics' => self::get_vendor_order_analytics($vendor_id, $period)
		);
	}

	/**
	 * Get vendor sales trend.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @param    int  $period     Period in days.
	 * @return   array            Sales trend data.
	 */
	private static function get_vendor_sales_trend($vendor_id, $period) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		return $wpdb->get_results($wpdb->prepare(
			"SELECT 
				DATE(created_at) as date,
				SUM(vendor_amount) as earnings,
				COUNT(DISTINCT order_id) as orders
			FROM $commissions_table 
			WHERE vendor_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY DATE(created_at)
			ORDER BY date ASC",
			$vendor_id,
			$period
		));
	}

	/**
	 * Get vendor product performance.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @param    int  $period     Period in days.
	 * @return   array            Product performance data.
	 */
	private static function get_vendor_product_performance($vendor_id, $period) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		return $wpdb->get_results($wpdb->prepare(
			"SELECT 
				c.product_id,
				p.post_title as product_name,
				SUM(c.vendor_amount) as earnings,
				COUNT(*) as units_sold,
				AVG(c.gross_amount) as avg_price
			FROM $commissions_table c
			JOIN {$wpdb->posts} p ON c.product_id = p.ID
			WHERE c.vendor_id = %d AND c.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY c.product_id
			ORDER BY earnings DESC",
			$vendor_id,
			$period
		));
	}

	/**
	 * Get vendor order analytics.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @param    int  $period     Period in days.
	 * @return   array            Order analytics data.
	 */
	private static function get_vendor_order_analytics($vendor_id, $period) {
		$orders = Multi_Vender_Book_Store_Order::get_vendor_orders($vendor_id, array(
			'limit' => 100
		));
		
		$analytics = array(
			'total_orders' => count($orders),
			'avg_order_value' => 0,
			'status_distribution' => array()
		);
		
		if (!empty($orders)) {
			$total_value = array_sum(wp_list_pluck($orders, 'total_amount'));
			$analytics['avg_order_value'] = $total_value / count($orders);
			
			// Status distribution
			$statuses = wp_list_pluck($orders, 'status');
			$analytics['status_distribution'] = array_count_values($statuses);
		}
		
		return $analytics;
	}
}
