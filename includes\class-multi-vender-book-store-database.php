<?php

/**
 * Database operations for the multi-vendor book store.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Database operations class.
 *
 * Handles all database operations for vendors, products, commissions,
 * messages, and other multi-vendor functionality.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Database {

	/**
	 * Get vendor by user ID.
	 *
	 * @since    1.0.0
	 * @param    int    $user_id    The user ID.
	 * @return   object|null         Vendor data or null if not found.
	 */
	public static function get_vendor_by_user_id($user_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_vendors';
		
		return $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM $table_name WHERE user_id = %d",
			$user_id
		));
	}

	/**
	 * Create a new vendor.
	 *
	 * @since    1.0.0
	 * @param    array  $vendor_data  Vendor information.
	 * @return   int|false            Vendor ID on success, false on failure.
	 */
	public static function create_vendor($vendor_data) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_vendors';
		
		$defaults = array(
			'status' => 'pending',
			'commission_rate' => get_option('mvbs_default_commission_rate', '10.00'),
			'created_at' => current_time('mysql'),
		);
		
		$vendor_data = wp_parse_args($vendor_data, $defaults);
		
		$result = $wpdb->insert(
			$table_name,
			$vendor_data,
			array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s', '%s')
		);
		
		return $result ? $wpdb->insert_id : false;
	}

	/**
	 * Update vendor information.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id    Vendor ID.
	 * @param    array  $vendor_data  Updated vendor data.
	 * @return   bool                 True on success, false on failure.
	 */
	public static function update_vendor($vendor_id, $vendor_data) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_vendors';
		
		$vendor_data['updated_at'] = current_time('mysql');
		
		$result = $wpdb->update(
			$table_name,
			$vendor_data,
			array('id' => $vendor_id),
			null,
			array('%d')
		);
		
		return $result !== false;
	}

	/**
	 * Get vendors with optional filtering and search.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Array of vendor objects.
	 */
	public static function get_vendors($args = array()) {
		global $wpdb;

		$defaults = array(
			'status' => '',
			'search' => '',
			'limit' => 20,
			'offset' => 0,
			'orderby' => 'created_at',
			'order' => 'DESC'
		);

		$args = wp_parse_args($args, $defaults);

		$table_name = $wpdb->prefix . 'mvbs_vendors';
		$where_conditions = array('1=1');
		$where_values = array();

		if (!empty($args['status'])) {
			$where_conditions[] = 'status = %s';
			$where_values[] = $args['status'];
		}

		if (!empty($args['search'])) {
			$where_conditions[] = '(business_name LIKE %s OR business_email LIKE %s)';
			$search_term = '%' . $wpdb->esc_like($args['search']) . '%';
			$where_values[] = $search_term;
			$where_values[] = $search_term;
		}

		$where_clause = implode(' AND ', $where_conditions);

		$query = $wpdb->prepare(
			"SELECT * FROM $table_name
			WHERE $where_clause
			ORDER BY {$args['orderby']} {$args['order']}
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);

		return $wpdb->get_results($query);
	}

	/**
	 * Add product to vendor.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id   Vendor ID.
	 * @param    int    $product_id  Product ID.
	 * @param    string $status      Product status.
	 * @return   int|false           Relationship ID on success, false on failure.
	 */
	public static function add_vendor_product($vendor_id, $product_id, $status = 'draft') {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_vendor_products';
		
		$result = $wpdb->insert(
			$table_name,
			array(
				'vendor_id' => $vendor_id,
				'product_id' => $product_id,
				'status' => $status,
				'created_at' => current_time('mysql')
			),
			array('%d', '%d', '%s', '%s')
		);
		
		return $result ? $wpdb->insert_id : false;
	}

	/**
	 * Get vendor products.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    array  $args       Query arguments.
	 * @return   array              Array of product relationships.
	 */
	public static function get_vendor_products($vendor_id, $args = array()) {
		global $wpdb;
		
		$defaults = array(
			'status' => '',
			'limit' => 20,
			'offset' => 0
		);
		
		$args = wp_parse_args($args, $defaults);
		
		$table_name = $wpdb->prefix . 'mvbs_vendor_products';
		$where_clause = 'vendor_id = %d';
		$where_values = array($vendor_id);
		
		if (!empty($args['status'])) {
			$where_clause .= ' AND status = %s';
			$where_values[] = $args['status'];
		}
		
		$query = $wpdb->prepare(
			"SELECT * FROM $table_name 
			WHERE $where_clause 
			ORDER BY created_at DESC 
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);
		
		return $wpdb->get_results($query);
	}

	/**
	 * Create commission record.
	 *
	 * @since    1.0.0
	 * @param    array  $commission_data  Commission information.
	 * @return   int|false                Commission ID on success, false on failure.
	 */
	public static function create_commission($commission_data) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_commissions';
		
		$defaults = array(
			'status' => 'pending',
			'created_at' => current_time('mysql')
		);
		
		$commission_data = wp_parse_args($commission_data, $defaults);
		
		$result = $wpdb->insert(
			$table_name,
			$commission_data,
			array('%d', '%d', '%d', '%d', '%f', '%f', '%f', '%f', '%s', '%s')
		);
		
		return $result ? $wpdb->insert_id : false;
	}

	/**
	 * Get vendor commissions.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    array  $args       Query arguments.
	 * @return   array              Array of commission records.
	 */
	public static function get_vendor_commissions($vendor_id, $args = array()) {
		global $wpdb;
		
		$defaults = array(
			'status' => '',
			'limit' => 20,
			'offset' => 0
		);
		
		$args = wp_parse_args($args, $defaults);
		
		$table_name = $wpdb->prefix . 'mvbs_commissions';
		$where_clause = 'vendor_id = %d';
		$where_values = array($vendor_id);
		
		if (!empty($args['status'])) {
			$where_clause .= ' AND status = %s';
			$where_values[] = $args['status'];
		}
		
		$query = $wpdb->prepare(
			"SELECT * FROM $table_name 
			WHERE $where_clause 
			ORDER BY created_at DESC 
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);
		
		return $wpdb->get_results($query);
	}

	/**
	 * Calculate commission amounts.
	 *
	 * @since    1.0.0
	 * @param    float  $gross_amount     Gross sale amount.
	 * @param    float  $commission_rate  Commission rate percentage.
	 * @return   array                    Array with commission and vendor amounts.
	 */
	public static function calculate_commission($gross_amount, $commission_rate) {
		$commission_amount = ($gross_amount * $commission_rate) / 100;
		$vendor_amount = $gross_amount - $commission_amount;
		
		return array(
			'commission_amount' => round($commission_amount, 2),
			'vendor_amount' => round($vendor_amount, 2)
		);
	}

	/**
	 * Get vendor statistics.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   array            Array of vendor statistics.
	 */
	public static function get_vendor_stats($vendor_id) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$products_table = $wpdb->prefix . 'mvbs_vendor_products';
		
		// Total earnings
		$total_earnings = $wpdb->get_var($wpdb->prepare(
			"SELECT SUM(vendor_amount) FROM $commissions_table WHERE vendor_id = %d AND status = 'paid'",
			$vendor_id
		));
		
		// Pending earnings
		$pending_earnings = $wpdb->get_var($wpdb->prepare(
			"SELECT SUM(vendor_amount) FROM $commissions_table WHERE vendor_id = %d AND status = 'pending'",
			$vendor_id
		));
		
		// Total products
		$total_products = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $products_table WHERE vendor_id = %d",
			$vendor_id
		));
		
		// Approved products
		$approved_products = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $products_table WHERE vendor_id = %d AND status = 'approved'",
			$vendor_id
		));
		
		return array(
			'total_earnings' => floatval($total_earnings),
			'pending_earnings' => floatval($pending_earnings),
			'total_products' => intval($total_products),
			'approved_products' => intval($approved_products)
		);
	}

	/**
	 * Get vendor status counts.
	 *
	 * @since    1.0.0
	 * @return   array  Status counts.
	 */
	public static function get_vendor_status_counts() {
		global $wpdb;

		$table_name = $wpdb->prefix . 'mvbs_vendors';

		$results = $wpdb->get_results(
			"SELECT status, COUNT(*) as count FROM $table_name GROUP BY status",
			ARRAY_A
		);

		$counts = array();
		foreach ($results as $result) {
			$counts[$result['status']] = intval($result['count']);
		}

		return $counts;
	}

	/**
	 * Get vendor product count.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   int              Product count.
	 */
	public static function get_vendor_product_count($vendor_id) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'mvbs_vendor_products';

		$count = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE vendor_id = %d AND status = 'approved'",
			$vendor_id
		));

		return intval($count);
	}


}
