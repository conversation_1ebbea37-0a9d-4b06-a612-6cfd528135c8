<?php

/**
 * Fired during plugin deactivation
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Deactivator {

	/**
	 * Plugin deactivation handler.
	 *
	 * Cleans up temporary data and flushes rewrite rules.
	 * Note: Database tables and user roles are preserved for data integrity.
	 *
	 * @since    1.0.0
	 */
	public static function deactivate() {
		// Clear any cached data
		wp_cache_flush();

		// Flush rewrite rules
		flush_rewrite_rules();

		// Clear scheduled events
		wp_clear_scheduled_hook('mvbs_process_pending_payments');
		wp_clear_scheduled_hook('mvbs_cleanup_old_api_logs');
		wp_clear_scheduled_hook('mvbs_send_vendor_notifications');
	}

}
