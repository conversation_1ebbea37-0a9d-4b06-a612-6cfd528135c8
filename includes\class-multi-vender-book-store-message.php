<?php

/**
 * Messaging system functionality for the multi-vendor system.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Messaging system class.
 *
 * Handles buyer-seller communication, message threading,
 * and notification system for the multi-vendor marketplace.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Message {

	/**
	 * Send a message between users.
	 *
	 * @since    1.0.0
	 * @param    array  $message_data  Message information.
	 * @return   array                 Send result with success/error info.
	 */
	public static function send_message($message_data) {
		// Validate required fields
		$required_fields = array('sender_id', 'recipient_id', 'message');
		foreach ($required_fields as $field) {
			if (empty($message_data[$field])) {
				return array(
					'success' => false,
					'message' => sprintf(__('Field %s is required.', 'multi-vender-book-store'), $field)
				);
			}
		}

		// Sanitize message data
		$message_record = array(
			'sender_id' => intval($message_data['sender_id']),
			'recipient_id' => intval($message_data['recipient_id']),
			'product_id' => isset($message_data['product_id']) ? intval($message_data['product_id']) : null,
			'order_id' => isset($message_data['order_id']) ? intval($message_data['order_id']) : null,
			'subject' => sanitize_text_field($message_data['subject'] ?? ''),
			'message' => sanitize_textarea_field($message_data['message']),
			'is_read' => 0,
			'created_at' => current_time('mysql')
		);

		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_messages';
		
		$result = $wpdb->insert(
			$table_name,
			$message_record,
			array('%d', '%d', '%d', '%d', '%s', '%s', '%d', '%s')
		);

		if ($result) {
			$message_id = $wpdb->insert_id;
			
			// Send email notification
			self::send_message_notification($message_id);
			
			return array(
				'success' => true,
				'message' => __('Message sent successfully.', 'multi-vender-book-store'),
				'message_id' => $message_id
			);
		} else {
			return array(
				'success' => false,
				'message' => __('Failed to send message.', 'multi-vender-book-store')
			);
		}
	}

	/**
	 * Get messages for a user.
	 *
	 * @since    1.0.0
	 * @param    int    $user_id  User ID.
	 * @param    array  $args     Query arguments.
	 * @return   array            Array of messages.
	 */
	public static function get_user_messages($user_id, $args = array()) {
		global $wpdb;
		
		$defaults = array(
			'type' => 'all', // 'sent', 'received', 'all'
			'limit' => 20,
			'offset' => 0,
			'unread_only' => false
		);
		
		$args = wp_parse_args($args, $defaults);
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		$where_conditions = array();
		$where_values = array();
		
		// Filter by message type
		switch ($args['type']) {
			case 'sent':
				$where_conditions[] = 'sender_id = %d';
				$where_values[] = $user_id;
				break;
			case 'received':
				$where_conditions[] = 'recipient_id = %d';
				$where_values[] = $user_id;
				break;
			default:
				$where_conditions[] = '(sender_id = %d OR recipient_id = %d)';
				$where_values[] = $user_id;
				$where_values[] = $user_id;
		}
		
		// Filter unread only
		if ($args['unread_only']) {
			$where_conditions[] = 'is_read = 0';
			$where_conditions[] = 'recipient_id = %d';
			$where_values[] = $user_id;
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = $wpdb->prepare(
			"SELECT * FROM $table_name 
			WHERE $where_clause 
			ORDER BY created_at DESC 
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);
		
		$messages = $wpdb->get_results($query);
		
		// Enhance messages with user data
		foreach ($messages as &$message) {
			$sender = get_user_by('ID', $message->sender_id);
			$recipient = get_user_by('ID', $message->recipient_id);
			
			$message->sender_name = $sender ? $sender->display_name : __('Unknown User', 'multi-vender-book-store');
			$message->recipient_name = $recipient ? $recipient->display_name : __('Unknown User', 'multi-vender-book-store');
			
			// Add product/order info if available
			if ($message->product_id) {
				$product = get_post($message->product_id);
				$message->product_title = $product ? $product->post_title : __('Unknown Product', 'multi-vender-book-store');
			}
			
			if ($message->order_id) {
				$message->order_number = '#' . $message->order_id;
			}
		}
		
		return $messages;
	}

	/**
	 * Get conversation between two users.
	 *
	 * @since    1.0.0
	 * @param    int  $user1_id  First user ID.
	 * @param    int  $user2_id  Second user ID.
	 * @param    int  $product_id Optional product ID to filter by.
	 * @return   array           Array of messages in conversation.
	 */
	public static function get_conversation($user1_id, $user2_id, $product_id = null) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		$where_conditions = array(
			'((sender_id = %d AND recipient_id = %d) OR (sender_id = %d AND recipient_id = %d))'
		);
		$where_values = array($user1_id, $user2_id, $user2_id, $user1_id);
		
		if ($product_id) {
			$where_conditions[] = 'product_id = %d';
			$where_values[] = $product_id;
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = $wpdb->prepare(
			"SELECT * FROM $table_name 
			WHERE $where_clause 
			ORDER BY created_at ASC",
			$where_values
		);
		
		$messages = $wpdb->get_results($query);
		
		// Mark messages as read for the current user
		if (is_user_logged_in()) {
			$current_user_id = get_current_user_id();
			self::mark_conversation_as_read($user1_id, $user2_id, $current_user_id, $product_id);
		}
		
		return $messages;
	}

	/**
	 * Mark message as read.
	 *
	 * @since    1.0.0
	 * @param    int  $message_id  Message ID.
	 * @param    int  $user_id     User ID (must be recipient).
	 * @return   bool              Success status.
	 */
	public static function mark_as_read($message_id, $user_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		
		$result = $wpdb->update(
			$table_name,
			array('is_read' => 1),
			array(
				'id' => $message_id,
				'recipient_id' => $user_id
			),
			array('%d'),
			array('%d', '%d')
		);
		
		return $result !== false;
	}

	/**
	 * Mark conversation as read.
	 *
	 * @since    1.0.0
	 * @param    int  $user1_id   First user ID.
	 * @param    int  $user2_id   Second user ID.
	 * @param    int  $reader_id  Reader user ID.
	 * @param    int  $product_id Optional product ID.
	 * @return   bool             Success status.
	 */
	public static function mark_conversation_as_read($user1_id, $user2_id, $reader_id, $product_id = null) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		$where_conditions = array(
			'((sender_id = %d AND recipient_id = %d) OR (sender_id = %d AND recipient_id = %d))',
			'recipient_id = %d',
			'is_read = 0'
		);
		$where_values = array($user1_id, $user2_id, $user2_id, $user1_id, $reader_id);
		
		if ($product_id) {
			$where_conditions[] = 'product_id = %d';
			$where_values[] = $product_id;
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = $wpdb->prepare(
			"UPDATE $table_name SET is_read = 1 WHERE $where_clause",
			$where_values
		);
		
		return $wpdb->query($query) !== false;
	}

	/**
	 * Get unread message count for user.
	 *
	 * @since    1.0.0
	 * @param    int  $user_id  User ID.
	 * @return   int            Unread message count.
	 */
	public static function get_unread_count($user_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		
		$count = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE recipient_id = %d AND is_read = 0",
			$user_id
		));
		
		return intval($count);
	}

	/**
	 * Send email notification for new message.
	 *
	 * @since    1.0.0
	 * @param    int  $message_id  Message ID.
	 */
	private static function send_message_notification($message_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		$message = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM $table_name WHERE id = %d",
			$message_id
		));
		
		if (!$message) {
			return;
		}
		
		$sender = get_user_by('ID', $message->sender_id);
		$recipient = get_user_by('ID', $message->recipient_id);
		
		if (!$sender || !$recipient) {
			return;
		}
		
		$subject = __('New Message Notification', 'multi-vender-book-store');
		$email_message = sprintf(
			__('Hello %s,\n\nYou have received a new message from %s.\n\nSubject: %s\nMessage: %s\n\nYou can reply to this message by logging into your account.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
			$recipient->display_name,
			$sender->display_name,
			$message->subject,
			$message->message
		);
		
		wp_mail($recipient->user_email, $subject, $email_message);
	}

	/**
	 * Delete message.
	 *
	 * @since    1.0.0
	 * @param    int  $message_id  Message ID.
	 * @param    int  $user_id     User ID (must be sender or recipient).
	 * @return   bool              Success status.
	 */
	public static function delete_message($message_id, $user_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_messages';
		
		// Verify user can delete this message
		$message = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM $table_name WHERE id = %d AND (sender_id = %d OR recipient_id = %d)",
			$message_id,
			$user_id,
			$user_id
		));
		
		if (!$message) {
			return false;
		}
		
		$result = $wpdb->delete(
			$table_name,
			array('id' => $message_id),
			array('%d')
		);
		
		return $result !== false;
	}
}
