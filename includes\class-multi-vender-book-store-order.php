<?php

/**
 * Order management functionality for the multi-vendor system.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Order management class.
 *
 * Handles order processing, commission calculation, and vendor
 * order management for the multi-vendor system.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Order {

	/**
	 * Process order and calculate commissions.
	 *
	 * @since    1.0.0
	 * @param    int  $order_id  WooCommerce order ID.
	 */
	public static function process_order($order_id) {
		if (!function_exists('wc_get_order')) {
			return;
		}

		$order = wc_get_order($order_id);
		if (!$order) {
			return;
		}

		foreach ($order->get_items() as $item_id => $item) {
			$product_id = $item->get_product_id();
			$quantity = $item->get_quantity();
			$line_total = $item->get_total();

			// Check if this is a vendor product
			$vendor = self::get_product_vendor($product_id);
			if ($vendor) {
				self::create_commission_record($vendor->id, $order_id, $product_id, $item_id, $line_total, $vendor->commission_rate);
			}
		}
	}

	/**
	 * Get vendor for a product.
	 *
	 * @since    1.0.0
	 * @param    int  $product_id  Product ID.
	 * @return   object|null       Vendor data or null.
	 */
	public static function get_product_vendor($product_id) {
		global $wpdb;
		
		$vendor_products_table = $wpdb->prefix . 'mvbs_vendor_products';
		$vendors_table = $wpdb->prefix . 'mvbs_vendors';
		
		$vendor = $wpdb->get_row($wpdb->prepare(
			"SELECT v.* FROM $vendors_table v 
			JOIN $vendor_products_table vp ON v.id = vp.vendor_id 
			WHERE vp.product_id = %d AND vp.status = 'approved'",
			$product_id
		));
		
		return $vendor;
	}

	/**
	 * Create commission record for vendor.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id       Vendor ID.
	 * @param    int    $order_id        Order ID.
	 * @param    int    $product_id      Product ID.
	 * @param    int    $order_item_id   Order item ID.
	 * @param    float  $gross_amount    Gross sale amount.
	 * @param    float  $commission_rate Commission rate.
	 */
	private static function create_commission_record($vendor_id, $order_id, $product_id, $order_item_id, $gross_amount, $commission_rate) {
		$commission_data = Multi_Vender_Book_Store_Database::calculate_commission($gross_amount, $commission_rate);
		
		$commission_record = array(
			'vendor_id' => $vendor_id,
			'order_id' => $order_id,
			'product_id' => $product_id,
			'order_item_id' => $order_item_id,
			'gross_amount' => $gross_amount,
			'commission_rate' => $commission_rate,
			'commission_amount' => $commission_data['commission_amount'],
			'vendor_amount' => $commission_data['vendor_amount']
		);
		
		Multi_Vender_Book_Store_Database::create_commission($commission_record);
		
		// Send notification to vendor
		self::notify_vendor_of_sale($vendor_id, $order_id, $product_id, $commission_data['vendor_amount']);
	}

	/**
	 * Get vendor orders.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    array  $args       Query arguments.
	 * @return   array              Array of order data.
	 */
	public static function get_vendor_orders($vendor_id, $args = array()) {
		global $wpdb;
		
		$defaults = array(
			'status' => '',
			'limit' => 20,
			'offset' => 0
		);
		
		$args = wp_parse_args($args, $defaults);
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$where_clause = 'vendor_id = %d';
		$where_values = array($vendor_id);
		
		if (!empty($args['status'])) {
			$where_clause .= ' AND status = %s';
			$where_values[] = $args['status'];
		}
		
		$query = $wpdb->prepare(
			"SELECT DISTINCT order_id, 
			SUM(vendor_amount) as total_amount,
			COUNT(*) as item_count,
			MAX(created_at) as order_date,
			status
			FROM $commissions_table 
			WHERE $where_clause 
			GROUP BY order_id 
			ORDER BY order_date DESC 
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);
		
		$orders = $wpdb->get_results($query);
		
		// Enhance with WooCommerce order data if available
		if (function_exists('wc_get_order')) {
			foreach ($orders as &$order_data) {
				$wc_order = wc_get_order($order_data->order_id);
				if ($wc_order) {
					$order_data->customer_name = $wc_order->get_billing_first_name() . ' ' . $wc_order->get_billing_last_name();
					$order_data->order_status = $wc_order->get_status();
					$order_data->order_total = $wc_order->get_total();
				}
			}
		}
		
		return $orders;
	}

	/**
	 * Get order details for vendor.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @param    int  $order_id   Order ID.
	 * @return   array            Order details.
	 */
	public static function get_vendor_order_details($vendor_id, $order_id) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		$items = $wpdb->get_results($wpdb->prepare(
			"SELECT c.*, p.post_title as product_name 
			FROM $commissions_table c 
			JOIN {$wpdb->posts} p ON c.product_id = p.ID 
			WHERE c.vendor_id = %d AND c.order_id = %d",
			$vendor_id,
			$order_id
		));
		
		$order_data = array(
			'items' => $items,
			'total_amount' => array_sum(wp_list_pluck($items, 'vendor_amount')),
			'commission_total' => array_sum(wp_list_pluck($items, 'commission_amount'))
		);
		
		// Add WooCommerce order data if available
		if (function_exists('wc_get_order')) {
			$wc_order = wc_get_order($order_id);
			if ($wc_order) {
				$order_data['customer'] = array(
					'name' => $wc_order->get_billing_first_name() . ' ' . $wc_order->get_billing_last_name(),
					'email' => $wc_order->get_billing_email(),
					'phone' => $wc_order->get_billing_phone(),
					'address' => $wc_order->get_formatted_billing_address()
				);
				$order_data['order_status'] = $wc_order->get_status();
				$order_data['order_date'] = $wc_order->get_date_created();
			}
		}
		
		return $order_data;
	}

	/**
	 * Update order item status.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    int    $order_id   Order ID.
	 * @param    string $status     New status.
	 * @return   bool               Success status.
	 */
	public static function update_vendor_order_status($vendor_id, $order_id, $status) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		$result = $wpdb->update(
			$commissions_table,
			array('status' => $status),
			array(
				'vendor_id' => $vendor_id,
				'order_id' => $order_id
			),
			array('%s'),
			array('%d', '%d')
		);
		
		return $result !== false;
	}

	/**
	 * Calculate vendor earnings summary.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   array            Earnings summary.
	 */
	public static function get_vendor_earnings_summary($vendor_id) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		
		$summary = $wpdb->get_row($wpdb->prepare(
			"SELECT 
			SUM(CASE WHEN status = 'paid' THEN vendor_amount ELSE 0 END) as total_paid,
			SUM(CASE WHEN status = 'pending' THEN vendor_amount ELSE 0 END) as total_pending,
			COUNT(DISTINCT order_id) as total_orders,
			COUNT(*) as total_items
			FROM $commissions_table 
			WHERE vendor_id = %d",
			$vendor_id
		), ARRAY_A);
		
		return array(
			'total_paid' => floatval($summary['total_paid']),
			'total_pending' => floatval($summary['total_pending']),
			'total_orders' => intval($summary['total_orders']),
			'total_items' => intval($summary['total_items'])
		);
	}

	/**
	 * Send sale notification to vendor.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id     Vendor ID.
	 * @param    int    $order_id      Order ID.
	 * @param    int    $product_id    Product ID.
	 * @param    float  $vendor_amount Vendor earnings.
	 */
	private static function notify_vendor_of_sale($vendor_id, $order_id, $product_id, $vendor_amount) {
		if (get_option('mvbs_order_notification_email', 'yes') !== 'yes') {
			return;
		}

		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($vendor_id);
		if (!$vendor) {
			return;
		}

		$user = get_user_by('ID', $vendor->user_id);
		$product = get_post($product_id);

		$subject = __('New Sale Notification', 'multi-vender-book-store');
		$message = sprintf(
			__('Hello %s,\n\nGreat news! You have a new sale:\n\nProduct: %s\nOrder ID: #%d\nYour Earnings: $%s\n\nYou can view the order details in your vendor dashboard.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
			$user->display_name,
			$product->post_title,
			$order_id,
			number_format($vendor_amount, 2)
		);
		
		wp_mail($user->user_email, $subject, $message);
	}

	/**
	 * Process payment request for vendor.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id      Vendor ID.
	 * @param    array  $commission_ids Commission IDs to include.
	 * @return   array                  Request result.
	 */
	public static function create_payment_request($vendor_id, $commission_ids) {
		global $wpdb;
		
		$commissions_table = $wpdb->prefix . 'mvbs_commissions';
		$payment_requests_table = $wpdb->prefix . 'mvbs_payment_requests';
		
		// Validate commission IDs belong to vendor and are pending
		$commission_ids_str = implode(',', array_map('intval', $commission_ids));
		$total_amount = $wpdb->get_var($wpdb->prepare(
			"SELECT SUM(vendor_amount) FROM $commissions_table 
			WHERE vendor_id = %d AND id IN ($commission_ids_str) AND status = 'pending'",
			$vendor_id
		));
		
		if (!$total_amount || $total_amount < get_option('mvbs_minimum_payout_amount', 50)) {
			return array(
				'success' => false,
				'message' => sprintf(__('Minimum payout amount is $%s', 'multi-vender-book-store'), get_option('mvbs_minimum_payout_amount', 50))
			);
		}
		
		// Create payment request
		$result = $wpdb->insert(
			$payment_requests_table,
			array(
				'vendor_id' => $vendor_id,
				'amount' => $total_amount,
				'commission_ids' => implode(',', $commission_ids),
				'status' => 'pending'
			),
			array('%d', '%f', '%s', '%s')
		);
		
		if ($result) {
			return array(
				'success' => true,
				'message' => __('Payment request submitted successfully.', 'multi-vender-book-store'),
				'request_id' => $wpdb->insert_id
			);
		} else {
			return array(
				'success' => false,
				'message' => __('Failed to create payment request.', 'multi-vender-book-store')
			);
		}
	}
}
