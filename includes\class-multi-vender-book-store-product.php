<?php

/**
 * Product management functionality for vendors.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Product management class.
 *
 * Handles vendor product creation, editing, approval workflow,
 * and product-specific operations.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Product {

	/**
	 * Create a new product for a vendor.
	 *
	 * @since    1.0.0
	 * @param    array  $product_data  Product information.
	 * @param    int    $vendor_id     Vendor ID.
	 * @return   array                 Creation result with success/error info.
	 */
	public static function create_vendor_product($product_data, $vendor_id) {
		// Validate required fields
		$required_fields = array('title', 'description', 'price', 'condition');
		foreach ($required_fields as $field) {
			if (empty($product_data[$field])) {
				return array(
					'success' => false,
					'message' => sprintf(__('Field %s is required.', 'multi-vender-book-store'), $field)
				);
			}
		}

		// Sanitize product data
		$post_data = array(
			'post_title'    => sanitize_text_field($product_data['title']),
			'post_content'  => wp_kses_post($product_data['description']),
			'post_status'   => 'draft', // Always start as draft
			'post_type'     => 'product',
			'post_author'   => get_current_user_id(),
			'meta_input'    => array(
				'is_physical_book' => 1,
				'_regular_price' => floatval($product_data['price']),
				'_price' => floatval($product_data['price']),
				'_manage_stock' => 'yes',
				'_stock' => intval($product_data['stock_quantity'] ?? 1),
				'_stock_status' => 'instock',
				'book_condition' => sanitize_text_field($product_data['condition']),
				'book_isbn' => sanitize_text_field($product_data['isbn'] ?? ''),
				'book_author' => sanitize_text_field($product_data['author'] ?? ''),
				'book_publisher' => sanitize_text_field($product_data['publisher'] ?? ''),
				'book_year' => intval($product_data['publication_year'] ?? 0),
				'book_language' => sanitize_text_field($product_data['language'] ?? ''),
				'book_pages' => intval($product_data['pages'] ?? 0),
				'vendor_notes' => sanitize_textarea_field($product_data['vendor_notes'] ?? ''),
			)
		);

		// Create the product post
		$product_id = wp_insert_post($post_data);

		if (is_wp_error($product_id)) {
			return array(
				'success' => false,
				'message' => $product_id->get_error_message()
			);
		}

		// Add to vendor products table
		$status = get_option('mvbs_auto_approve_products', 'no') === 'yes' ? 'approved' : 'pending';
		$vendor_product_id = Multi_Vender_Book_Store_Database::add_vendor_product($vendor_id, $product_id, $status);

		if (!$vendor_product_id) {
			// Clean up product if vendor relationship creation failed
			wp_delete_post($product_id, true);
			return array(
				'success' => false,
				'message' => __('Failed to create vendor product relationship.', 'multi-vender-book-store')
			);
		}

		// Handle image uploads
		if (!empty($_FILES['product_images'])) {
			self::handle_product_images($product_id, $_FILES['product_images']);
		}

		// Set product categories if provided
		if (!empty($product_data['categories'])) {
			wp_set_post_terms($product_id, $product_data['categories'], 'product_cat');
		}

		// Send notification emails
		self::send_product_submission_emails($product_id, $vendor_id);

		return array(
			'success' => true,
			'message' => $status === 'approved' ? 
				__('Product created and approved successfully.', 'multi-vender-book-store') :
				__('Product created successfully. Waiting for admin approval.', 'multi-vender-book-store'),
			'product_id' => $product_id
		);
	}

	/**
	 * Update vendor product.
	 *
	 * @since    1.0.0
	 * @param    int    $product_id    Product ID.
	 * @param    array  $product_data  Updated product data.
	 * @param    int    $vendor_id     Vendor ID.
	 * @return   array                 Update result.
	 */
	public static function update_vendor_product($product_id, $product_data, $vendor_id) {
		// Verify vendor owns this product
		if (!self::vendor_owns_product($vendor_id, $product_id)) {
			return array(
				'success' => false,
				'message' => __('Unauthorized access to product.', 'multi-vender-book-store')
			);
		}

		// Prepare update data
		$post_data = array(
			'ID' => $product_id,
			'post_title' => sanitize_text_field($product_data['title']),
			'post_content' => wp_kses_post($product_data['description']),
		);

		// Update the post
		$result = wp_update_post($post_data);

		if (is_wp_error($result)) {
			return array(
				'success' => false,
				'message' => $result->get_error_message()
			);
		}

		// Update meta fields
		$meta_fields = array(
			'_regular_price' => floatval($product_data['price']),
			'_price' => floatval($product_data['price']),
			'_stock' => intval($product_data['stock_quantity'] ?? 1),
			'book_condition' => sanitize_text_field($product_data['condition']),
			'book_isbn' => sanitize_text_field($product_data['isbn'] ?? ''),
			'book_author' => sanitize_text_field($product_data['author'] ?? ''),
			'book_publisher' => sanitize_text_field($product_data['publisher'] ?? ''),
			'book_year' => intval($product_data['publication_year'] ?? 0),
			'book_language' => sanitize_text_field($product_data['language'] ?? ''),
			'book_pages' => intval($product_data['pages'] ?? 0),
			'vendor_notes' => sanitize_textarea_field($product_data['vendor_notes'] ?? ''),
		);

		foreach ($meta_fields as $key => $value) {
			update_post_meta($product_id, $key, $value);
		}

		// Handle new image uploads
		if (!empty($_FILES['product_images'])) {
			self::handle_product_images($product_id, $_FILES['product_images']);
		}

		// Update categories
		if (isset($product_data['categories'])) {
			wp_set_post_terms($product_id, $product_data['categories'], 'product_cat');
		}

		return array(
			'success' => true,
			'message' => __('Product updated successfully.', 'multi-vender-book-store')
		);
	}

	/**
	 * Get vendor products with filtering.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id  Vendor ID.
	 * @param    array  $args       Query arguments.
	 * @return   array              Array of products.
	 */
	public static function get_vendor_products($vendor_id, $args = array()) {
		$defaults = array(
			'status' => '',
			'posts_per_page' => 20,
			'paged' => 1,
			'orderby' => 'date',
			'order' => 'DESC'
		);

		$args = wp_parse_args($args, $defaults);

		// Get vendor product relationships
		$vendor_products = Multi_Vender_Book_Store_Database::get_vendor_products($vendor_id, array(
			'status' => $args['status'],
			'limit' => $args['posts_per_page'],
			'offset' => ($args['paged'] - 1) * $args['posts_per_page']
		));

		if (empty($vendor_products)) {
			return array();
		}

		// Get product IDs
		$product_ids = wp_list_pluck($vendor_products, 'product_id');

		// Query products
		$query_args = array(
			'post_type' => 'product',
			'post__in' => $product_ids,
			'posts_per_page' => -1,
			'orderby' => $args['orderby'],
			'order' => $args['order'],
			'meta_query' => array(
				array(
					'key' => 'is_physical_book',
					'value' => 1,
					'compare' => '='
				)
			)
		);

		$products = get_posts($query_args);

		// Add vendor relationship data to products
		foreach ($products as &$product) {
			$vendor_product = array_filter($vendor_products, function($vp) use ($product) {
				return $vp->product_id == $product->ID;
			});
			$vendor_product = reset($vendor_product);
			$product->vendor_status = $vendor_product ? $vendor_product->status : 'unknown';
		}

		return $products;
	}

	/**
	 * Check if vendor owns a product.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id   Vendor ID.
	 * @param    int  $product_id  Product ID.
	 * @return   bool              True if vendor owns product.
	 */
	public static function vendor_owns_product($vendor_id, $product_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_vendor_products';
		
		$result = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE vendor_id = %d AND product_id = %d",
			$vendor_id,
			$product_id
		));
		
		return $result > 0;
	}

	/**
	 * Handle product image uploads.
	 *
	 * @since    1.0.0
	 * @param    int    $product_id  Product ID.
	 * @param    array  $files       Files array from $_FILES.
	 */
	private static function handle_product_images($product_id, $files) {
		if (!function_exists('wp_handle_upload')) {
			require_once(ABSPATH . 'wp-admin/includes/file.php');
		}

		$uploaded_files = array();
		
		// Handle multiple files
		if (is_array($files['name'])) {
			for ($i = 0; $i < count($files['name']); $i++) {
				if ($files['error'][$i] === UPLOAD_ERR_OK) {
					$file = array(
						'name' => $files['name'][$i],
						'type' => $files['type'][$i],
						'tmp_name' => $files['tmp_name'][$i],
						'error' => $files['error'][$i],
						'size' => $files['size'][$i]
					);
					
					$upload = wp_handle_upload($file, array('test_form' => false));
					
					if (!isset($upload['error'])) {
						$uploaded_files[] = $upload;
					}
				}
			}
		} else {
			// Single file
			if ($files['error'] === UPLOAD_ERR_OK) {
				$upload = wp_handle_upload($files, array('test_form' => false));
				
				if (!isset($upload['error'])) {
					$uploaded_files[] = $upload;
				}
			}
		}

		// Create attachments and set as product images
		foreach ($uploaded_files as $index => $upload) {
			$attachment = array(
				'post_mime_type' => $upload['type'],
				'post_title' => sanitize_file_name($upload['file']),
				'post_content' => '',
				'post_status' => 'inherit'
			);

			$attachment_id = wp_insert_attachment($attachment, $upload['file'], $product_id);

			if (!is_wp_error($attachment_id)) {
				require_once(ABSPATH . 'wp-admin/includes/image.php');
				$attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
				wp_update_attachment_metadata($attachment_id, $attachment_data);

				// Set first image as featured image
				if ($index === 0) {
					set_post_thumbnail($product_id, $attachment_id);
				}

				// Add to product gallery
				$gallery_ids = get_post_meta($product_id, '_product_image_gallery', true);
				$gallery_ids = $gallery_ids ? explode(',', $gallery_ids) : array();
				$gallery_ids[] = $attachment_id;
				update_post_meta($product_id, '_product_image_gallery', implode(',', $gallery_ids));
			}
		}
	}

	/**
	 * Send product submission notification emails.
	 *
	 * @since    1.0.0
	 * @param    int  $product_id  Product ID.
	 * @param    int  $vendor_id   Vendor ID.
	 */
	private static function send_product_submission_emails($product_id, $vendor_id) {
		if (get_option('mvbs_product_approval_email', 'yes') !== 'yes') {
			return;
		}

		$product = get_post($product_id);
		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id(get_current_user_id());
		$user = get_user_by('ID', $vendor->user_id);

		// Email to vendor
		$subject = __('Product Submission Confirmation', 'multi-vender-book-store');
		$message = sprintf(
			__('Hello %s,\n\nYour product "%s" has been submitted successfully.\n\nIt is currently under review and will be published once approved.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
			$user->display_name,
			$product->post_title
		);
		
		wp_mail($user->user_email, $subject, $message);

		// Email to admin
		$admin_email = get_option('admin_email');
		$admin_subject = __('New Product Submission', 'multi-vender-book-store');
		$admin_message = sprintf(
			__('A new product has been submitted for review:\n\nProduct: %s\nVendor: %s\nPrice: $%s\n\nPlease review and approve the product.', 'multi-vender-book-store'),
			$product->post_title,
			$vendor->business_name,
			get_post_meta($product_id, '_regular_price', true)
		);
		
		wp_mail($admin_email, $admin_subject, $admin_message);
	}

	/**
	 * Approve vendor product.
	 *
	 * @since    1.0.0
	 * @param    int  $product_id  Product ID.
	 * @return   bool              True on success.
	 */
	public static function approve_product($product_id) {
		if (!current_user_can('approve_vendor_products')) {
			return false;
		}

		// Update product status to published
		wp_update_post(array(
			'ID' => $product_id,
			'post_status' => 'publish'
		));

		// Update vendor product relationship
		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_vendor_products';
		
		$result = $wpdb->update(
			$table_name,
			array('status' => 'approved'),
			array('product_id' => $product_id),
			array('%s'),
			array('%d')
		);

		return $result !== false;
	}
}
