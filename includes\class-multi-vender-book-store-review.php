<?php

/**
 * Review and rating system functionality for the multi-vendor system.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Review system class.
 *
 * Handles customer reviews and ratings for vendors and products
 * in the multi-vendor marketplace system.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Review {

	/**
	 * Add a review for a vendor or product.
	 *
	 * @since    1.0.0
	 * @param    array  $review_data  Review information.
	 * @return   array                Review result with success/error info.
	 */
	public static function add_review($review_data) {
		// Validate required fields
		$required_fields = array('reviewer_id', 'rating', 'review_text');
		foreach ($required_fields as $field) {
			if (empty($review_data[$field])) {
				return array(
					'success' => false,
					'message' => sprintf(__('Field %s is required.', 'multi-vender-book-store'), $field)
				);
			}
		}

		// Validate rating
		$rating = intval($review_data['rating']);
		if ($rating < 1 || $rating > 5) {
			return array(
				'success' => false,
				'message' => __('Rating must be between 1 and 5 stars.', 'multi-vender-book-store')
			);
		}

		// Check if user has already reviewed this vendor/product
		if (self::has_user_reviewed($review_data['reviewer_id'], $review_data['vendor_id'] ?? null, $review_data['product_id'] ?? null)) {
			return array(
				'success' => false,
				'message' => __('You have already reviewed this item.', 'multi-vender-book-store')
			);
		}

		// Create review record
		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_reviews';
		
		$review_record = array(
			'reviewer_id' => intval($review_data['reviewer_id']),
			'vendor_id' => isset($review_data['vendor_id']) ? intval($review_data['vendor_id']) : null,
			'product_id' => isset($review_data['product_id']) ? intval($review_data['product_id']) : null,
			'order_id' => isset($review_data['order_id']) ? intval($review_data['order_id']) : null,
			'rating' => $rating,
			'review_text' => sanitize_textarea_field($review_data['review_text']),
			'status' => 'approved', // Auto-approve for now
			'created_at' => current_time('mysql')
		);

		$result = $wpdb->insert(
			$table_name,
			$review_record,
			array('%d', '%d', '%d', '%d', '%d', '%s', '%s', '%s')
		);

		if ($result) {
			$review_id = $wpdb->insert_id;
			
			// Update average ratings
			self::update_average_ratings($review_data['vendor_id'] ?? null, $review_data['product_id'] ?? null);
			
			return array(
				'success' => true,
				'message' => __('Review submitted successfully.', 'multi-vender-book-store'),
				'review_id' => $review_id
			);
		} else {
			return array(
				'success' => false,
				'message' => __('Failed to submit review.', 'multi-vender-book-store')
			);
		}
	}

	/**
	 * Get reviews for a vendor or product.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Array of reviews.
	 */
	public static function get_reviews($args = array()) {
		global $wpdb;
		
		$defaults = array(
			'vendor_id' => null,
			'product_id' => null,
			'status' => 'approved',
			'limit' => 20,
			'offset' => 0,
			'orderby' => 'created_at',
			'order' => 'DESC'
		);
		
		$args = wp_parse_args($args, $defaults);
		
		$table_name = $wpdb->prefix . 'mvbs_reviews';
		$where_conditions = array();
		$where_values = array();
		
		if ($args['vendor_id']) {
			$where_conditions[] = 'vendor_id = %d';
			$where_values[] = $args['vendor_id'];
		}
		
		if ($args['product_id']) {
			$where_conditions[] = 'product_id = %d';
			$where_values[] = $args['product_id'];
		}
		
		if ($args['status']) {
			$where_conditions[] = 'status = %s';
			$where_values[] = $args['status'];
		}
		
		$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
		
		$query = $wpdb->prepare(
			"SELECT * FROM $table_name 
			$where_clause 
			ORDER BY {$args['orderby']} {$args['order']} 
			LIMIT %d OFFSET %d",
			array_merge($where_values, array($args['limit'], $args['offset']))
		);
		
		$reviews = $wpdb->get_results($query);
		
		// Enhance reviews with user data
		foreach ($reviews as &$review) {
			$reviewer = get_user_by('ID', $review->reviewer_id);
			$review->reviewer_name = $reviewer ? $reviewer->display_name : __('Anonymous', 'multi-vender-book-store');
			$review->reviewer_avatar = get_avatar_url($review->reviewer_id, array('size' => 48));
			
			if ($review->product_id) {
				$product = get_post($review->product_id);
				$review->product_title = $product ? $product->post_title : __('Unknown Product', 'multi-vender-book-store');
			}
			
			if ($review->vendor_id) {
				$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($review->vendor_id);
				$review->vendor_name = $vendor ? $vendor->business_name : __('Unknown Vendor', 'multi-vender-book-store');
			}
		}
		
		return $reviews;
	}

	/**
	 * Get average rating for vendor or product.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id   Vendor ID (optional).
	 * @param    int  $product_id  Product ID (optional).
	 * @return   array             Rating data.
	 */
	public static function get_average_rating($vendor_id = null, $product_id = null) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_reviews';
		$where_conditions = array('status = %s');
		$where_values = array('approved');
		
		if ($vendor_id) {
			$where_conditions[] = 'vendor_id = %d';
			$where_values[] = $vendor_id;
		}
		
		if ($product_id) {
			$where_conditions[] = 'product_id = %d';
			$where_values[] = $product_id;
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = $wpdb->prepare(
			"SELECT 
				AVG(rating) as average_rating,
				COUNT(*) as total_reviews,
				SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
				SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
				SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
				SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
				SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
			FROM $table_name 
			WHERE $where_clause",
			$where_values
		);
		
		$result = $wpdb->get_row($query, ARRAY_A);
		
		return array(
			'average_rating' => round(floatval($result['average_rating']), 1),
			'total_reviews' => intval($result['total_reviews']),
			'rating_breakdown' => array(
				5 => intval($result['five_star']),
				4 => intval($result['four_star']),
				3 => intval($result['three_star']),
				2 => intval($result['two_star']),
				1 => intval($result['one_star'])
			)
		);
	}

	/**
	 * Check if user has already reviewed vendor/product.
	 *
	 * @since    1.0.0
	 * @param    int  $user_id     User ID.
	 * @param    int  $vendor_id   Vendor ID (optional).
	 * @param    int  $product_id  Product ID (optional).
	 * @return   bool              True if already reviewed.
	 */
	public static function has_user_reviewed($user_id, $vendor_id = null, $product_id = null) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_reviews';
		$where_conditions = array('reviewer_id = %d');
		$where_values = array($user_id);
		
		if ($vendor_id) {
			$where_conditions[] = 'vendor_id = %d';
			$where_values[] = $vendor_id;
		}
		
		if ($product_id) {
			$where_conditions[] = 'product_id = %d';
			$where_values[] = $product_id;
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$count = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE $where_clause",
			$where_values
		));
		
		return intval($count) > 0;
	}

	/**
	 * Update average ratings in vendor/product meta.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id   Vendor ID (optional).
	 * @param    int  $product_id  Product ID (optional).
	 */
	private static function update_average_ratings($vendor_id = null, $product_id = null) {
		if ($vendor_id) {
			$rating_data = self::get_average_rating($vendor_id, null);
			
			global $wpdb;
			$vendors_table = $wpdb->prefix . 'mvbs_vendors';
			
			$wpdb->update(
				$vendors_table,
				array(
					'average_rating' => $rating_data['average_rating'],
					'total_reviews' => $rating_data['total_reviews']
				),
				array('id' => $vendor_id),
				array('%f', '%d'),
				array('%d')
			);
		}
		
		if ($product_id) {
			$rating_data = self::get_average_rating(null, $product_id);
			
			update_post_meta($product_id, '_average_rating', $rating_data['average_rating']);
			update_post_meta($product_id, '_review_count', $rating_data['total_reviews']);
		}
	}

	/**
	 * Get review statistics for dashboard.
	 *
	 * @since    1.0.0
	 * @param    array  $args  Query arguments.
	 * @return   array         Review statistics.
	 */
	public static function get_review_statistics($args = array()) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'mvbs_reviews';
		$period = intval($args['period'] ?? 30);

		// Check if table exists, if not create it
		if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
			self::create_reviews_table();
		}
		
		// Recent reviews
		$recent_reviews = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				r.*,
				v.business_name as vendor_name,
				p.post_title as product_title
			FROM $table_name r
			LEFT JOIN {$wpdb->prefix}mvbs_vendors v ON r.vendor_id = v.id
			LEFT JOIN {$wpdb->posts} p ON r.product_id = p.ID
			WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			ORDER BY r.created_at DESC
			LIMIT 10",
			$period
		));
		
		// Review trends
		$review_trends = $wpdb->get_results($wpdb->prepare(
			"SELECT 
				DATE(created_at) as date,
				COUNT(*) as review_count,
				AVG(rating) as avg_rating
			FROM $table_name 
			WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
			GROUP BY DATE(created_at)
			ORDER BY date ASC",
			$period
		));
		
		// Overall statistics
		$overall_stats = $wpdb->get_row(
			"SELECT 
				COUNT(*) as total_reviews,
				AVG(rating) as overall_avg_rating,
				COUNT(DISTINCT vendor_id) as vendors_with_reviews,
				COUNT(DISTINCT product_id) as products_with_reviews
			FROM $table_name 
			WHERE status = 'approved'"
		);
		
		return array(
			'recent_reviews' => $recent_reviews,
			'review_trends' => $review_trends,
			'overall_stats' => $overall_stats
		);
	}

	/**
	 * Render star rating HTML.
	 *
	 * @since    1.0.0
	 * @param    float  $rating     Rating value.
	 * @param    int    $max_stars  Maximum stars.
	 * @return   string             Star rating HTML.
	 */
	public static function render_star_rating($rating, $max_stars = 5) {
		$rating = floatval($rating);
		$full_stars = floor($rating);
		$half_star = ($rating - $full_stars) >= 0.5;
		$empty_stars = $max_stars - $full_stars - ($half_star ? 1 : 0);
		
		$html = '<div class="mvbs-star-rating" data-rating="' . $rating . '">';
		
		// Full stars
		for ($i = 0; $i < $full_stars; $i++) {
			$html .= '<span class="mvbs-star mvbs-star-full">★</span>';
		}
		
		// Half star
		if ($half_star) {
			$html .= '<span class="mvbs-star mvbs-star-half">★</span>';
		}
		
		// Empty stars
		for ($i = 0; $i < $empty_stars; $i++) {
			$html .= '<span class="mvbs-star mvbs-star-empty">☆</span>';
		}
		
		$html .= '</div>';
		
		return $html;
	}

	/**
	 * Delete a review.
	 *
	 * @since    1.0.0
	 * @param    int  $review_id  Review ID.
	 * @param    int  $user_id    User ID (must be reviewer or admin).
	 * @return   bool             Success status.
	 */
	public static function delete_review($review_id, $user_id) {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'mvbs_reviews';
		
		// Get review to verify ownership
		$review = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM $table_name WHERE id = %d",
			$review_id
		));
		
		if (!$review) {
			return false;
		}
		
		// Check if user can delete (owner or admin)
		if ($review->reviewer_id != $user_id && !current_user_can('manage_options')) {
			return false;
		}
		
		$result = $wpdb->delete(
			$table_name,
			array('id' => $review_id),
			array('%d')
		);
		
		if ($result) {
			// Update average ratings
			self::update_average_ratings($review->vendor_id, $review->product_id);
		}
		
		return $result !== false;
	}

	/**
	 * Create reviews table if it doesn't exist.
	 *
	 * @since    1.0.0
	 */
	private static function create_reviews_table() {
		global $wpdb;

		$table_name = $wpdb->prefix . 'mvbs_reviews';
		$charset_collate = $wpdb->get_charset_collate();

		$sql = "CREATE TABLE $table_name (
			id mediumint(9) NOT NULL AUTO_INCREMENT,
			reviewer_id bigint(20) NOT NULL,
			vendor_id mediumint(9),
			product_id bigint(20),
			order_id bigint(20),
			rating tinyint(1) NOT NULL,
			review_text text NOT NULL,
			status varchar(20) DEFAULT 'approved',
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY reviewer_id (reviewer_id),
			KEY vendor_id (vendor_id),
			KEY product_id (product_id),
			KEY rating (rating),
			KEY status (status),
			KEY created_at (created_at)
		) $charset_collate;";

		require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
		dbDelta($sql);
	}
}
