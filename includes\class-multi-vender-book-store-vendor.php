<?php

/**
 * Vendor management functionality.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 */

/**
 * Vendor management class.
 *
 * Handles vendor registration, authentication, profile management,
 * and vendor-specific operations.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/includes
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Vendor {

	/**
	 * Register a new vendor.
	 *
	 * @since    1.0.0
	 * @param    array  $vendor_data  Vendor registration data.
	 * @return   array                Registration result with success/error info.
	 */
	public static function register_vendor($vendor_data) {
		// Validate required fields
		$required_fields = array('username', 'email', 'password');
		foreach ($required_fields as $field) {
			if (empty($vendor_data[$field])) {
				return array(
					'success' => false,
					'message' => sprintf(__('Field %s is required.', 'multi-vender-book-store'), $field)
				);
			}
		}

		// Check if username already exists
		if (username_exists($vendor_data['username'])) {
			return array(
				'success' => false,
				'message' => __('Username already exists.', 'multi-vender-book-store')
			);
		}

		// Check if email already exists
		if (email_exists($vendor_data['email'])) {
			return array(
				'success' => false,
				'message' => __('Email already exists.', 'multi-vender-book-store')
			);
		}

		// Create WordPress user
		$user_id = wp_create_user(
			$vendor_data['username'],
			$vendor_data['password'],
			$vendor_data['email']
		);

		if (is_wp_error($user_id)) {
			return array(
				'success' => false,
				'message' => $user_id->get_error_message()
			);
		}

		// Set user role to vendor
		$user = new WP_User($user_id);
		$user->set_role('vendor');

		// Update user meta
		update_user_meta($user_id, 'first_name', sanitize_text_field($vendor_data['first_name'] ?? ''));
		update_user_meta($user_id, 'last_name', sanitize_text_field($vendor_data['last_name'] ?? ''));

		// Create vendor record with default business info
		$display_name = trim(($vendor_data['first_name'] ?? '') . ' ' . ($vendor_data['last_name'] ?? ''));
		if (empty($display_name)) {
			$display_name = $vendor_data['username'];
		}

		$vendor_record = array(
			'user_id' => $user_id,
			'business_name' => sanitize_text_field($display_name),
			'business_email' => sanitize_email($vendor_data['email']),
			'business_phone' => '',
			'business_address' => '',
			'tax_id' => '',
			'status' => get_option('mvbs_auto_approve_vendors', 'no') === 'yes' ? 'approved' : 'pending'
		);

		$vendor_id = Multi_Vender_Book_Store_Database::create_vendor($vendor_record);

		if (!$vendor_id) {
			// Clean up user if vendor creation failed
			wp_delete_user($user_id);
			return array(
				'success' => false,
				'message' => __('Failed to create vendor record.', 'multi-vender-book-store')
			);
		}

		// Send notification emails
		self::send_vendor_registration_emails($user_id, $vendor_id);

		return array(
			'success' => true,
			'message' => __('Vendor registration successful. Please wait for approval.', 'multi-vender-book-store'),
			'user_id' => $user_id,
			'vendor_id' => $vendor_id
		);
	}

	/**
	 * Authenticate vendor login.
	 *
	 * @since    1.0.0
	 * @param    string $username  Username or email.
	 * @param    string $password  Password.
	 * @return   array             Authentication result.
	 */
	public static function authenticate_vendor($username, $password) {
		$user = wp_authenticate($username, $password);

		if (is_wp_error($user)) {
			return array(
				'success' => false,
				'message' => $user->get_error_message()
			);
		}

		// Check if user is a vendor
		if (!in_array('vendor', $user->roles)) {
			return array(
				'success' => false,
				'message' => __('Access denied. Vendor account required.', 'multi-vender-book-store')
			);
		}

		// Check vendor status
		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($user->ID);
		if (!$vendor) {
			return array(
				'success' => false,
				'message' => __('Vendor record not found.', 'multi-vender-book-store')
			);
		}

		if ($vendor->status !== 'approved') {
			$status_messages = array(
				'pending' => __('Your vendor account is pending approval.', 'multi-vender-book-store'),
				'suspended' => __('Your vendor account has been suspended.', 'multi-vender-book-store'),
				'rejected' => __('Your vendor application has been rejected.', 'multi-vender-book-store')
			);

			return array(
				'success' => false,
				'message' => $status_messages[$vendor->status] ?? __('Account access denied.', 'multi-vender-book-store')
			);
		}

		// Log in the user
		wp_set_current_user($user->ID);
		wp_set_auth_cookie($user->ID, true);

		return array(
			'success' => true,
			'message' => __('Login successful.', 'multi-vender-book-store'),
			'user_id' => $user->ID,
			'vendor_id' => $vendor->id
		);
	}

	/**
	 * Get current vendor information.
	 *
	 * @since    1.0.0
	 * @return   object|null  Vendor data or null if not found.
	 */
	public static function get_current_vendor() {
		if (!is_user_logged_in()) {
			return null;
		}

		$current_user = wp_get_current_user();
		if (!in_array('vendor', $current_user->roles)) {
			return null;
		}

		return Multi_Vender_Book_Store_Database::get_vendor_by_user_id($current_user->ID);
	}

	/**
	 * Update vendor profile.
	 *
	 * @since    1.0.0
	 * @param    int    $vendor_id    Vendor ID.
	 * @param    array  $profile_data Updated profile data.
	 * @return   array                Update result.
	 */
	public static function update_vendor_profile($vendor_id, $profile_data) {
		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id(get_current_user_id());
		
		if (!$vendor || $vendor->id != $vendor_id) {
			return array(
				'success' => false,
				'message' => __('Unauthorized access.', 'multi-vender-book-store')
			);
		}

		// Sanitize data
		$update_data = array();
		$allowed_fields = array('business_name', 'business_email', 'business_phone', 'business_address', 'tax_id', 'bank_account');
		
		foreach ($allowed_fields as $field) {
			if (isset($profile_data[$field])) {
				if ($field === 'business_email') {
					$update_data[$field] = sanitize_email($profile_data[$field]);
				} elseif ($field === 'business_address') {
					$update_data[$field] = sanitize_textarea_field($profile_data[$field]);
				} else {
					$update_data[$field] = sanitize_text_field($profile_data[$field]);
				}
			}
		}

		if (empty($update_data)) {
			return array(
				'success' => false,
				'message' => __('No valid data to update.', 'multi-vender-book-store')
			);
		}

		$result = Multi_Vender_Book_Store_Database::update_vendor($vendor_id, $update_data);

		return array(
			'success' => $result,
			'message' => $result ? __('Profile updated successfully.', 'multi-vender-book-store') : __('Failed to update profile.', 'multi-vender-book-store')
		);
	}

	/**
	 * Check if current user can manage vendor.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID to check.
	 * @return   bool             True if user can manage vendor.
	 */
	public static function can_manage_vendor($vendor_id) {
		if (!is_user_logged_in()) {
			return false;
		}

		$current_user = wp_get_current_user();
		
		// Administrators can manage all vendors
		if (in_array('administrator', $current_user->roles)) {
			return true;
		}

		// Vendors can only manage their own account
		if (in_array('vendor', $current_user->roles)) {
			$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($current_user->ID);
			return $vendor && $vendor->id == $vendor_id;
		}

		return false;
	}

	/**
	 * Send vendor registration notification emails.
	 *
	 * @since    1.0.0
	 * @param    int  $user_id   User ID.
	 * @param    int  $vendor_id Vendor ID.
	 */
	private static function send_vendor_registration_emails($user_id, $vendor_id) {
		if (get_option('mvbs_vendor_registration_email', 'yes') !== 'yes') {
			return;
		}

		$user = get_user_by('ID', $user_id);
		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($user_id);

		// Email to vendor
		$subject = __('Vendor Registration Confirmation', 'multi-vender-book-store');
		$message = sprintf(
			__('Hello %s,\n\nThank you for registering as a vendor on our platform.\n\nYour application is currently under review. You will receive another email once your account is approved.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
			$user->display_name
		);
		
		wp_mail($user->user_email, $subject, $message);

		// Email to admin
		$admin_email = get_option('admin_email');
		$admin_subject = __('New Vendor Registration', 'multi-vender-book-store');
		$admin_message = sprintf(
			__('A new vendor has registered:\n\nBusiness Name: %s\nEmail: %s\nUser: %s\n\nPlease review and approve the vendor account.', 'multi-vender-book-store'),
			$vendor->business_name,
			$vendor->business_email,
			$user->user_login
		);
		
		wp_mail($admin_email, $admin_subject, $admin_message);
	}

	/**
	 * Approve vendor account.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   bool             True on success.
	 */
	public static function approve_vendor($vendor_id) {
		// Allow administrators to approve vendors
		if (!current_user_can('manage_options')) {
			return false;
		}

		$result = Multi_Vender_Book_Store_Database::update_vendor($vendor_id, array('status' => 'approved'));
		
		if ($result) {
			// Send approval email
			$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($vendor_id);
			if ($vendor) {
				$user = get_user_by('ID', $vendor->user_id);
				$subject = __('Vendor Account Approved', 'multi-vender-book-store');
				$message = sprintf(
					__('Hello %s,\n\nCongratulations! Your vendor account has been approved.\n\nYou can now log in and start managing your products.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
					$user->display_name
				);
				
				wp_mail($user->user_email, $subject, $message);
			}
		}

		return $result;
	}

	/**
	 * Reject a vendor.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   bool             Success status.
	 */
	public static function reject_vendor($vendor_id) {
		if (!current_user_can('manage_options')) {
			return false;
		}

		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_vendors';

		$result = $wpdb->update(
			$table_name,
			array('status' => 'rejected'),
			array('id' => $vendor_id),
			array('%s'),
			array('%d')
		);

		if ($result) {
			// Send rejection email
			$vendor = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $vendor_id));
			if ($vendor) {
				$user = get_user_by('ID', $vendor->user_id);
				if ($user) {
					$subject = __('Vendor Application Status', 'multi-vender-book-store');
					$message = sprintf(
						__('Hello %s,\n\nWe regret to inform you that your vendor application has been rejected.\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
						$user->display_name
					);
					wp_mail($user->user_email, $subject, $message);
				}
			}
		}

		return $result !== false;
	}

	/**
	 * Suspend a vendor.
	 *
	 * @since    1.0.0
	 * @param    int  $vendor_id  Vendor ID.
	 * @return   bool             Success status.
	 */
	public static function suspend_vendor($vendor_id) {
		if (!current_user_can('manage_options')) {
			return false;
		}

		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_vendors';

		$result = $wpdb->update(
			$table_name,
			array('status' => 'suspended'),
			array('id' => $vendor_id),
			array('%s'),
			array('%d')
		);

		if ($result) {
			// Send suspension email
			$vendor = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $vendor_id));
			if ($vendor) {
				$user = get_user_by('ID', $vendor->user_id);
				if ($user) {
					$subject = __('Account Suspended', 'multi-vender-book-store');
					$message = sprintf(
						__('Hello %s,\n\nYour vendor account has been temporarily suspended.\n\nPlease contact our support team for more information.\n\nBest regards,\nThe Team', 'multi-vender-book-store'),
						$user->display_name
					);
					wp_mail($user->user_email, $subject, $message);
				}
			}
		}

		return $result !== false;
	}
}
