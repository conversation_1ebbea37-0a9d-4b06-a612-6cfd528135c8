<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://www.fiverr.com/websdev
 * @since             1.0.0
 * @package           Multi_Vender_Book_Store
 *
 * @wordpress-plugin
 * Plugin Name:       Multi Vender Book Store
 * Plugin URI:        https://bokakaup.is
 * Description:       The plugin converts the store into Multi Vender Book Store where multiple sellers can register their account and start selling books
 * Version:           1.0.0
 * Author:            websdev
 * Author URI:        https://www.fiverr.com/websdev/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       multi-vender-book-store
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.0 and use Sem<PERSON>er - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define( 'MULTI_VENDER_BOOK_STORE_VERSION', '1.0.0' );

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-multi-vender-book-store-activator.php
 */
function activate_multi_vender_book_store() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-multi-vender-book-store-activator.php';
	Multi_Vender_Book_Store_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-multi-vender-book-store-deactivator.php
 */
function deactivate_multi_vender_book_store() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-multi-vender-book-store-deactivator.php';
	Multi_Vender_Book_Store_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_multi_vender_book_store' );
register_deactivation_hook( __FILE__, 'deactivate_multi_vender_book_store' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-multi-vender-book-store.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */
function run_multi_vender_book_store() {

	$plugin = new Multi_Vender_Book_Store();
	$plugin->run();

}
run_multi_vender_book_store();
