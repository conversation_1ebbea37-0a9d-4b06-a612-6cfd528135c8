<?php

/**
 * Messaging system for vendor-customer communication.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 */

/**
 * Messaging system class.
 *
 * <PERSON>les frontend messaging interface for vendor-customer communication.
 *
 * @since      1.0.0
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Messaging {

	/**
	 * Initialize messaging system.
	 *
	 * @since    1.0.0
	 */
	public static function init() {
		add_shortcode('mvbs_messaging', array(__CLASS__, 'messaging_shortcode'));
		add_shortcode('mvbs_contact_vendor', array(__CLASS__, 'contact_vendor_shortcode'));
		add_action('wp_ajax_mvbs_send_message', array(__CLASS__, 'ajax_send_message'));
		add_action('wp_ajax_nopriv_mvbs_send_message', array(__CLASS__, 'ajax_send_message'));
		add_action('wp_ajax_mvbs_load_messages', array(__CLASS__, 'ajax_load_messages'));
		add_action('wp_ajax_nopriv_mvbs_load_messages', array(__CLASS__, 'ajax_load_messages'));
	}

	/**
	 * Messaging interface shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Messaging interface HTML.
	 */
	public static function messaging_shortcode($atts) {
		if (!is_user_logged_in()) {
			return '<p>' . __('Please log in to access messaging.', 'multi-vender-book-store') . '</p>';
		}

		$atts = shortcode_atts(array(
			'vendor_id' => '',
			'product_id' => ''
		), $atts);

		ob_start();
		?>
		<div id="mvbs-messaging-container" class="mvbs-messaging">
			<div class="mvbs-messaging-header">
				<h3><?php _e('Messages', 'multi-vender-book-store'); ?></h3>
			</div>
			
			<div class="mvbs-messaging-content">
				<div class="mvbs-conversations-list">
					<h4><?php _e('Conversations', 'multi-vender-book-store'); ?></h4>
					<div id="mvbs-conversations">
						<?php echo self::get_conversations_html(); ?>
					</div>
				</div>
				
				<div class="mvbs-message-thread">
					<div id="mvbs-messages" class="mvbs-messages-list">
						<p class="mvbs-select-conversation"><?php _e('Select a conversation to view messages', 'multi-vender-book-store'); ?></p>
					</div>
					
					<div id="mvbs-message-form" class="mvbs-message-form" style="display: none;">
						<form id="mvbs-send-message-form">
							<div class="mvbs-form-group">
								<textarea id="mvbs-message-text" placeholder="<?php _e('Type your message...', 'multi-vender-book-store'); ?>" required></textarea>
							</div>
							<div class="mvbs-form-actions">
								<button type="submit" class="mvbs-btn mvbs-btn-primary"><?php _e('Send Message', 'multi-vender-book-store'); ?></button>
							</div>
							<input type="hidden" id="mvbs-recipient-id" value="">
							<input type="hidden" id="mvbs-product-id" value="">
						</form>
					</div>
				</div>
			</div>
		</div>

		<style>
		.mvbs-messaging {
			max-width: 800px;
			margin: 20px auto;
			border: 1px solid #ddd;
			border-radius: 8px;
			overflow: hidden;
		}
		.mvbs-messaging-header {
			background: #f8f9fa;
			padding: 15px 20px;
			border-bottom: 1px solid #ddd;
		}
		.mvbs-messaging-header h3 {
			margin: 0;
			color: #333;
		}
		.mvbs-messaging-content {
			display: flex;
			min-height: 400px;
		}
		.mvbs-conversations-list {
			width: 300px;
			border-right: 1px solid #ddd;
			background: #f8f9fa;
			padding: 15px;
		}
		.mvbs-conversations-list h4 {
			margin: 0 0 15px 0;
			color: #333;
		}
		.mvbs-conversation-item {
			padding: 10px;
			border: 1px solid #ddd;
			border-radius: 4px;
			margin-bottom: 10px;
			cursor: pointer;
			background: white;
			transition: background 0.3s;
		}
		.mvbs-conversation-item:hover {
			background: #e9ecef;
		}
		.mvbs-conversation-item.active {
			background: #007cba;
			color: white;
		}
		.mvbs-conversation-name {
			font-weight: bold;
			margin-bottom: 5px;
		}
		.mvbs-conversation-preview {
			font-size: 12px;
			color: #666;
		}
		.mvbs-conversation-item.active .mvbs-conversation-preview {
			color: #ccc;
		}
		.mvbs-message-thread {
			flex: 1;
			display: flex;
			flex-direction: column;
		}
		.mvbs-messages-list {
			flex: 1;
			padding: 15px;
			overflow-y: auto;
			max-height: 300px;
		}
		.mvbs-message {
			margin-bottom: 15px;
			padding: 10px;
			border-radius: 8px;
			max-width: 70%;
		}
		.mvbs-message.sent {
			background: #007cba;
			color: white;
			margin-left: auto;
		}
		.mvbs-message.received {
			background: #f1f1f1;
			color: #333;
		}
		.mvbs-message-meta {
			font-size: 11px;
			opacity: 0.7;
			margin-top: 5px;
		}
		.mvbs-message-form {
			border-top: 1px solid #ddd;
			padding: 15px;
		}
		.mvbs-form-group {
			margin-bottom: 10px;
		}
		.mvbs-form-group textarea {
			width: 100%;
			min-height: 80px;
			padding: 10px;
			border: 1px solid #ddd;
			border-radius: 4px;
			resize: vertical;
		}
		.mvbs-btn {
			padding: 8px 16px;
			border: none;
			border-radius: 4px;
			cursor: pointer;
			font-size: 14px;
		}
		.mvbs-btn-primary {
			background: #007cba;
			color: white;
		}
		.mvbs-btn-primary:hover {
			background: #005a87;
		}
		.mvbs-select-conversation {
			text-align: center;
			color: #666;
			font-style: italic;
			margin-top: 50px;
		}
		</style>

		<script>
		jQuery(document).ready(function($) {
			// Handle conversation selection
			$(document).on('click', '.mvbs-conversation-item', function() {
				$('.mvbs-conversation-item').removeClass('active');
				$(this).addClass('active');
				
				var recipientId = $(this).data('user-id');
				var productId = $(this).data('product-id') || '';
				
				$('#mvbs-recipient-id').val(recipientId);
				$('#mvbs-product-id').val(productId);
				
				loadMessages(recipientId, productId);
				$('#mvbs-message-form').show();
			});
			
			// Handle message sending
			$('#mvbs-send-message-form').on('submit', function(e) {
				e.preventDefault();
				
				var messageText = $('#mvbs-message-text').val().trim();
				var recipientId = $('#mvbs-recipient-id').val();
				var productId = $('#mvbs-product-id').val();
				
				if (!messageText || !recipientId) {
					return;
				}
				
				$.ajax({
					url: mvbs_ajax.ajax_url,
					type: 'POST',
					data: {
						action: 'mvbs_send_message',
						recipient_id: recipientId,
						product_id: productId,
						message: messageText,
						nonce: mvbs_ajax.nonce
					},
					success: function(response) {
						if (response.success) {
							$('#mvbs-message-text').val('');
							loadMessages(recipientId, productId);
						} else {
							alert(response.data || 'Failed to send message');
						}
					}
				});
			});
			
			function loadMessages(recipientId, productId) {
				$.ajax({
					url: mvbs_ajax.ajax_url,
					type: 'POST',
					data: {
						action: 'mvbs_load_messages',
						recipient_id: recipientId,
						product_id: productId,
						nonce: mvbs_ajax.nonce
					},
					success: function(response) {
						if (response.success) {
							$('#mvbs-messages').html(response.data);
							$('#mvbs-messages').scrollTop($('#mvbs-messages')[0].scrollHeight);
						}
					}
				});
			}
		});
		</script>
		<?php
		return ob_get_clean();
	}

	/**
	 * Contact vendor shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Contact form HTML.
	 */
	public static function contact_vendor_shortcode($atts) {
		if (!is_user_logged_in()) {
			return '<p>' . __('Please log in to contact vendors.', 'multi-vender-book-store') . '</p>';
		}

		$atts = shortcode_atts(array(
			'vendor_id' => '',
			'product_id' => ''
		), $atts);

		if (empty($atts['vendor_id'])) {
			return '<p>' . __('Vendor ID is required.', 'multi-vender-book-store') . '</p>';
		}

		$vendor = Multi_Vender_Book_Store_Database::get_vendor_by_user_id($atts['vendor_id']);
		if (!$vendor) {
			return '<p>' . __('Vendor not found.', 'multi-vender-book-store') . '</p>';
		}

		ob_start();
		?>
		<div class="mvbs-contact-vendor">
			<h3><?php printf(__('Contact %s', 'multi-vender-book-store'), esc_html($vendor->business_name)); ?></h3>
			
			<form id="mvbs-contact-vendor-form" class="mvbs-contact-form">
				<div class="mvbs-form-group">
					<label for="mvbs-contact-subject"><?php _e('Subject', 'multi-vender-book-store'); ?></label>
					<input type="text" id="mvbs-contact-subject" name="subject" required>
				</div>
				
				<div class="mvbs-form-group">
					<label for="mvbs-contact-message"><?php _e('Message', 'multi-vender-book-store'); ?></label>
					<textarea id="mvbs-contact-message" name="message" rows="5" required></textarea>
				</div>
				
				<div class="mvbs-form-actions">
					<button type="submit" class="mvbs-btn mvbs-btn-primary"><?php _e('Send Message', 'multi-vender-book-store'); ?></button>
				</div>
				
				<input type="hidden" name="vendor_id" value="<?php echo esc_attr($atts['vendor_id']); ?>">
				<input type="hidden" name="product_id" value="<?php echo esc_attr($atts['product_id']); ?>">
			</form>
		</div>

		<script>
		jQuery(document).ready(function($) {
			$('#mvbs-contact-vendor-form').on('submit', function(e) {
				e.preventDefault();
				
				var formData = $(this).serialize();
				
				$.ajax({
					url: mvbs_ajax.ajax_url,
					type: 'POST',
					data: formData + '&action=mvbs_send_message&nonce=' + mvbs_ajax.nonce,
					success: function(response) {
						if (response.success) {
							alert('<?php _e('Message sent successfully!', 'multi-vender-book-store'); ?>');
							$('#mvbs-contact-vendor-form')[0].reset();
						} else {
							alert(response.data || 'Failed to send message');
						}
					}
				});
			});
		});
		</script>
		<?php
		return ob_get_clean();
	}

	/**
	 * Get conversations HTML for current user.
	 *
	 * @since    1.0.0
	 * @return   string  Conversations HTML.
	 */
	private static function get_conversations_html() {
		$current_user_id = get_current_user_id();
		$conversations = Multi_Vender_Book_Store_Message::get_user_conversations($current_user_id);

		if (empty($conversations)) {
			return '<p>' . __('No conversations yet.', 'multi-vender-book-store') . '</p>';
		}

		$html = '';
		foreach ($conversations as $conversation) {
			$other_user = get_user_by('ID', $conversation->other_user_id);
			if (!$other_user) continue;

			$unread_class = $conversation->unread_count > 0 ? 'unread' : '';

			$html .= '<div class="mvbs-conversation-item ' . $unread_class . '" data-user-id="' . $conversation->other_user_id . '" data-product-id="' . ($conversation->product_id ?: '') . '">';
			$html .= '<div class="mvbs-conversation-name">' . esc_html($other_user->display_name) . '</div>';
			$html .= '<div class="mvbs-conversation-preview">' . esc_html(wp_trim_words($conversation->last_message, 8)) . '</div>';
			if ($conversation->unread_count > 0) {
				$html .= '<div class="mvbs-unread-count">' . $conversation->unread_count . '</div>';
			}
			$html .= '</div>';
		}

		return $html;
	}

	/**
	 * AJAX handler for sending messages.
	 *
	 * @since    1.0.0
	 */
	public static function ajax_send_message() {
		check_ajax_referer('mvbs_ajax_nonce', 'nonce');

		if (!is_user_logged_in()) {
			wp_send_json_error(__('Please log in to send messages.', 'multi-vender-book-store'));
		}

		$recipient_id = intval($_POST['recipient_id']);
		$product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : null;
		$message = sanitize_textarea_field($_POST['message']);
		$subject = isset($_POST['subject']) ? sanitize_text_field($_POST['subject']) : __('New Message', 'multi-vender-book-store');

		if (empty($recipient_id) || empty($message)) {
			wp_send_json_error(__('Recipient and message are required.', 'multi-vender-book-store'));
		}

		$result = Multi_Vender_Book_Store_Message::send_message(array(
			'sender_id' => get_current_user_id(),
			'recipient_id' => $recipient_id,
			'product_id' => $product_id,
			'subject' => $subject,
			'message' => $message
		));

		if ($result['success']) {
			wp_send_json_success(__('Message sent successfully.', 'multi-vender-book-store'));
		} else {
			wp_send_json_error($result['message']);
		}
	}

	/**
	 * AJAX handler for loading messages.
	 *
	 * @since    1.0.0
	 */
	public static function ajax_load_messages() {
		check_ajax_referer('mvbs_ajax_nonce', 'nonce');

		if (!is_user_logged_in()) {
			wp_send_json_error(__('Please log in to view messages.', 'multi-vender-book-store'));
		}

		$recipient_id = intval($_POST['recipient_id']);
		$product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : null;
		$current_user_id = get_current_user_id();

		$messages = Multi_Vender_Book_Store_Message::get_conversation($current_user_id, $recipient_id, $product_id);

		$html = '';
		foreach ($messages as $message) {
			$is_sent = $message->sender_id == $current_user_id;
			$class = $is_sent ? 'sent' : 'received';

			$html .= '<div class="mvbs-message ' . $class . '">';
			$html .= '<div class="mvbs-message-content">' . esc_html($message->message) . '</div>';
			$html .= '<div class="mvbs-message-meta">' . date('M j, Y g:i A', strtotime($message->created_at)) . '</div>';
			$html .= '</div>';
		}

		if (empty($html)) {
			$html = '<p class="mvbs-no-messages">' . __('No messages yet. Start the conversation!', 'multi-vender-book-store') . '</p>';
		}

		wp_send_json_success($html);
	}
}
