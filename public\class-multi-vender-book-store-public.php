<?php

/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Public {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of the plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add shortcodes
		add_action('init', array($this, 'register_shortcodes'));

	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-public.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-public.js', array( 'jquery' ), $this->version, false );

	}

	/**
	 * Register shortcodes.
	 *
	 * @since    1.0.0
	 */
	public function register_shortcodes() {
		add_shortcode('mvbs_vendor_registration', array($this, 'vendor_registration_shortcode'));
		add_shortcode('mvbs_vendor_login', array($this, 'vendor_login_shortcode'));
		add_shortcode('mvbs_vendor_dashboard', array($this, 'vendor_dashboard_shortcode'));
		add_shortcode('mvbs_messaging', array($this, 'messaging_shortcode'));
	}

	/**
	 * Vendor registration shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Registration form HTML.
	 */
	public function vendor_registration_shortcode($atts) {
		$atts = shortcode_atts(array(
			'redirect' => ''
		), $atts);

		// Handle form submission
		if (isset($_POST['mvbs_register_vendor'])) {
			$result = Multi_Vender_Book_Store_Vendor::register_vendor($_POST);

			if ($result['success']) {
				return '<div class="mvbs-success">' . esc_html($result['message']) . '</div>';
			} else {
				$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
			}
		}

		ob_start();
		?>
		<div class="mvbs-vendor-registration">
			<?php if (isset($error_message)) echo $error_message; ?>

			<form method="post" action="">
				<?php wp_nonce_field('mvbs_vendor_registration', 'mvbs_nonce'); ?>

				<h3><?php _e('Personal Information', 'multi-vender-book-store'); ?></h3>

				<p>
					<label for="first_name"><?php _e('First Name', 'multi-vender-book-store'); ?></label>
					<input type="text" name="first_name" id="first_name" value="<?php echo esc_attr($_POST['first_name'] ?? ''); ?>" />
				</p>

				<p>
					<label for="last_name"><?php _e('Last Name', 'multi-vender-book-store'); ?></label>
					<input type="text" name="last_name" id="last_name" value="<?php echo esc_attr($_POST['last_name'] ?? ''); ?>" />
				</p>

				<p>
					<label for="username"><?php _e('Username *', 'multi-vender-book-store'); ?></label>
					<input type="text" name="username" id="username" required value="<?php echo esc_attr($_POST['username'] ?? ''); ?>" />
				</p>

				<p>
					<label for="email"><?php _e('Email *', 'multi-vender-book-store'); ?></label>
					<input type="email" name="email" id="email" required value="<?php echo esc_attr($_POST['email'] ?? ''); ?>" />
				</p>

				<p>
					<label for="password"><?php _e('Password *', 'multi-vender-book-store'); ?></label>
					<input type="password" name="password" id="password" required />
				</p>

				<h3><?php _e('Business Information', 'multi-vender-book-store'); ?></h3>

				<p>
					<label for="business_name"><?php _e('Business Name *', 'multi-vender-book-store'); ?></label>
					<input type="text" name="business_name" id="business_name" required value="<?php echo esc_attr($_POST['business_name'] ?? ''); ?>" />
				</p>

				<p>
					<label for="business_email"><?php _e('Business Email *', 'multi-vender-book-store'); ?></label>
					<input type="email" name="business_email" id="business_email" required value="<?php echo esc_attr($_POST['business_email'] ?? ''); ?>" />
				</p>

				<p>
					<label for="business_phone"><?php _e('Business Phone', 'multi-vender-book-store'); ?></label>
					<input type="tel" name="business_phone" id="business_phone" value="<?php echo esc_attr($_POST['business_phone'] ?? ''); ?>" />
				</p>

				<p>
					<label for="business_address"><?php _e('Business Address', 'multi-vender-book-store'); ?></label>
					<textarea name="business_address" id="business_address" rows="3"><?php echo esc_textarea($_POST['business_address'] ?? ''); ?></textarea>
				</p>

				<p>
					<label for="tax_id"><?php _e('Tax ID', 'multi-vender-book-store'); ?></label>
					<input type="text" name="tax_id" id="tax_id" value="<?php echo esc_attr($_POST['tax_id'] ?? ''); ?>" />
				</p>

				<p>
					<input type="submit" name="mvbs_register_vendor" value="<?php _e('Register as Vendor', 'multi-vender-book-store'); ?>" class="button button-primary" />
				</p>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Vendor login shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Login form HTML.
	 */
	public function vendor_login_shortcode($atts) {
		$atts = shortcode_atts(array(
			'redirect' => ''
		), $atts);

		// If user is already logged in and is a vendor, redirect to dashboard
		if (is_user_logged_in()) {
			$current_user = wp_get_current_user();
			if (in_array('vendor', $current_user->roles)) {
				$dashboard_page_id = get_option('mvbs_vendor_dashboard_page_id');
				if ($dashboard_page_id) {
					$dashboard_url = get_permalink($dashboard_page_id);
					return '<p>' . sprintf(__('You are already logged in. <a href="%s">Go to Dashboard</a>', 'multi-vender-book-store'), $dashboard_url) . '</p>';
				}
			}
		}

		// Handle form submission
		if (isset($_POST['mvbs_vendor_login'])) {
			$result = Multi_Vender_Book_Store_Vendor::authenticate_vendor($_POST['username'], $_POST['password']);

			if ($result['success']) {
				$redirect_url = !empty($atts['redirect']) ? $atts['redirect'] : get_permalink(get_option('mvbs_vendor_dashboard_page_id'));
				wp_redirect($redirect_url);
				exit;
			} else {
				$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
			}
		}

		ob_start();
		?>
		<div class="mvbs-vendor-login">
			<?php if (isset($error_message)) echo $error_message; ?>

			<form method="post" action="">
				<?php wp_nonce_field('mvbs_vendor_login', 'mvbs_nonce'); ?>

				<p>
					<label for="username"><?php _e('Username or Email', 'multi-vender-book-store'); ?></label>
					<input type="text" name="username" id="username" required value="<?php echo esc_attr($_POST['username'] ?? ''); ?>" />
				</p>

				<p>
					<label for="password"><?php _e('Password', 'multi-vender-book-store'); ?></label>
					<input type="password" name="password" id="password" required />
				</p>

				<p>
					<input type="submit" name="mvbs_vendor_login" value="<?php _e('Login', 'multi-vender-book-store'); ?>" class="button button-primary" />
				</p>

				<p>
					<a href="<?php echo wp_lostpassword_url(); ?>"><?php _e('Lost Password?', 'multi-vender-book-store'); ?></a>
				</p>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Vendor dashboard shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Dashboard HTML.
	 */
	public function vendor_dashboard_shortcode($atts) {
		$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

		if (!$vendor) {
			$login_page_id = get_option('mvbs_vendor_login_page_id');
			$login_url = $login_page_id ? get_permalink($login_page_id) : wp_login_url();
			return '<p>' . sprintf(__('Please <a href="%s">login</a> to access your vendor dashboard.', 'multi-vender-book-store'), $login_url) . '</p>';
		}

		if ($vendor->status !== 'approved') {
			return '<p>' . __('Your vendor account is pending approval. Please wait for admin approval.', 'multi-vender-book-store') . '</p>';
		}

		$stats = Multi_Vender_Book_Store_Database::get_vendor_stats($vendor->id);

		ob_start();
		?>
		<div class="mvbs-vendor-dashboard">
			<h2><?php printf(__('Welcome, %s', 'multi-vender-book-store'), esc_html($vendor->business_name)); ?></h2>

			<div class="mvbs-dashboard-stats">
				<div class="mvbs-stat-box">
					<h3><?php _e('Total Products', 'multi-vender-book-store'); ?></h3>
					<p class="mvbs-stat-number"><?php echo intval($stats['total_products']); ?></p>
				</div>

				<div class="mvbs-stat-box">
					<h3><?php _e('Approved Products', 'multi-vender-book-store'); ?></h3>
					<p class="mvbs-stat-number"><?php echo intval($stats['approved_products']); ?></p>
				</div>

				<div class="mvbs-stat-box">
					<h3><?php _e('Total Earnings', 'multi-vender-book-store'); ?></h3>
					<p class="mvbs-stat-number">$<?php echo number_format($stats['total_earnings'], 2); ?></p>
				</div>

				<div class="mvbs-stat-box">
					<h3><?php _e('Pending Earnings', 'multi-vender-book-store'); ?></h3>
					<p class="mvbs-stat-number">$<?php echo number_format($stats['pending_earnings'], 2); ?></p>
				</div>
			</div>

			<div class="mvbs-dashboard-actions">
				<a href="#" class="button button-primary"><?php _e('Add Product', 'multi-vender-book-store'); ?></a>
				<a href="#" class="button"><?php _e('Manage Products', 'multi-vender-book-store'); ?></a>
				<a href="#" class="button"><?php _e('View Orders', 'multi-vender-book-store'); ?></a>
				<a href="#" class="button"><?php _e('Messages', 'multi-vender-book-store'); ?></a>
				<a href="<?php echo wp_logout_url(); ?>" class="button"><?php _e('Logout', 'multi-vender-book-store'); ?></a>
			</div>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Messaging shortcode placeholder.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Messaging HTML.
	 */
	public function messaging_shortcode($atts) {
		return '<div class="mvbs-messaging"><p>' . __('Messaging system coming soon...', 'multi-vender-book-store') . '</p></div>';
	}

}
