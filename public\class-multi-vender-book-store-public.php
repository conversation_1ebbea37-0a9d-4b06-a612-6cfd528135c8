<?php

/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://www.fiverr.com/websdev
 * @since      1.0.0
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    Multi_Vender_Book_Store
 * @subpackage Multi_Vender_Book_Store/public
 * <AUTHOR> <<EMAIL>>
 */
class Multi_Vender_Book_Store_Public {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of the plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add shortcodes
		add_action('init', array($this, 'register_shortcodes'));

		// WooCommerce integration
		add_action('woocommerce_single_product_summary', array($this, 'add_contact_vendor_button'), 25);

	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/multi-vender-book-store-public.css', array(), $this->version, 'all' );

	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts() {

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Multi_Vender_Book_Store_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Multi_Vender_Book_Store_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/multi-vender-book-store-public.js', array( 'jquery' ), $this->version, false );

		// Localize script for AJAX
		wp_localize_script( $this->plugin_name, 'mvbs_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce'    => wp_create_nonce( 'mvbs_ajax_nonce' )
		));

	}

	/**
	 * Register shortcodes.
	 *
	 * @since    1.0.0
	 */
	public function register_shortcodes() {
		add_shortcode('mvbs_vendor_registration', array($this, 'vendor_registration_shortcode'));
		add_shortcode('mvbs_vendor_login', array($this, 'vendor_login_shortcode'));
		add_shortcode('mvbs_vendor_dashboard', array($this, 'vendor_dashboard_shortcode'));
		add_shortcode('mvbs_messaging', array($this, 'messaging_shortcode'));
		add_shortcode('mvbs_add_product', array($this, 'add_product_shortcode'));
		add_shortcode('mvbs_manage_products', array($this, 'manage_products_shortcode'));
		add_shortcode('mvbs_edit_product', array($this, 'edit_product_shortcode'));
		add_shortcode('mvbs_vendor_orders', array($this, 'vendor_orders_shortcode'));
		add_shortcode('mvbs_vendor_profile', array($this, 'public_vendor_profile_shortcode'));
	}

	/**
	 * Vendor registration shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Registration form HTML.
	 */
	public function vendor_registration_shortcode($atts) {
		$atts = shortcode_atts(array(
			'redirect' => ''
		), $atts);

		// Handle form submission
		if (isset($_POST['mvbs_register_vendor'])) {
			$result = Multi_Vender_Book_Store_Vendor::register_vendor($_POST);

			if ($result['success']) {
				return '<div class="mvbs-success">' . esc_html($result['message']) . '</div>';
			} else {
				$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
			}
		}

		ob_start();
		?>
		<div class="mvbs-vendor-registration">
			<div class="mvbs-registration-header">
				<h2><?php _e('Join as a Vendor', 'multi-vender-book-store'); ?></h2>
				<p class="mvbs-registration-subtitle"><?php _e('Start selling your books today', 'multi-vender-book-store'); ?></p>
			</div>

			<?php if (isset($error_message)) echo $error_message; ?>

			<form method="post" action="" class="mvbs-registration-form">
				<?php wp_nonce_field('mvbs_vendor_registration', 'mvbs_nonce'); ?>

				<div class="mvbs-form-grid">
					<div class="mvbs-form-group">
						<label for="first_name"><?php _e('First Name', 'multi-vender-book-store'); ?></label>
						<input type="text" name="first_name" id="first_name" value="<?php echo esc_attr($_POST['first_name'] ?? ''); ?>" placeholder="<?php _e('Enter your first name', 'multi-vender-book-store'); ?>" />
					</div>

					<div class="mvbs-form-group">
						<label for="last_name"><?php _e('Last Name', 'multi-vender-book-store'); ?></label>
						<input type="text" name="last_name" id="last_name" value="<?php echo esc_attr($_POST['last_name'] ?? ''); ?>" placeholder="<?php _e('Enter your last name', 'multi-vender-book-store'); ?>" />
					</div>
				</div>

				<div class="mvbs-form-group">
					<label for="username"><?php _e('Username', 'multi-vender-book-store'); ?> <span class="mvbs-required">*</span></label>
					<input type="text" name="username" id="username" required value="<?php echo esc_attr($_POST['username'] ?? ''); ?>" placeholder="<?php _e('Choose a unique username', 'multi-vender-book-store'); ?>" />
				</div>

				<div class="mvbs-form-group">
					<label for="email"><?php _e('Email Address', 'multi-vender-book-store'); ?> <span class="mvbs-required">*</span></label>
					<input type="email" name="email" id="email" required value="<?php echo esc_attr($_POST['email'] ?? ''); ?>" placeholder="<?php _e('<EMAIL>', 'multi-vender-book-store'); ?>" />
				</div>

				<div class="mvbs-form-group">
					<label for="password"><?php _e('Password', 'multi-vender-book-store'); ?> <span class="mvbs-required">*</span></label>
					<input type="password" name="password" id="password" required placeholder="<?php _e('Create a strong password', 'multi-vender-book-store'); ?>" />
					<small class="mvbs-form-help"><?php _e('Minimum 8 characters recommended', 'multi-vender-book-store'); ?></small>
				</div>

				<div class="mvbs-form-actions">
					<button type="submit" name="mvbs_register_vendor" class="mvbs-btn mvbs-btn-primary mvbs-btn-large">
						<span class="mvbs-btn-icon">🚀</span>
						<?php _e('Create Vendor Account', 'multi-vender-book-store'); ?>
					</button>
				</div>

				<div class="mvbs-form-footer">
					<p><?php _e('Already have an account?', 'multi-vender-book-store'); ?>
					<a href="<?php echo get_permalink(get_option('mvbs_vendor_login_page_id')); ?>" class="mvbs-link"><?php _e('Sign in here', 'multi-vender-book-store'); ?></a></p>
				</div>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Vendor login shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Login form HTML.
	 */
	public function vendor_login_shortcode($atts) {
		$atts = shortcode_atts(array(
			'redirect' => ''
		), $atts);

		// If user is already logged in and is a vendor, redirect to dashboard
		if (is_user_logged_in()) {
			$current_user = wp_get_current_user();
			if (in_array('vendor', $current_user->roles)) {
				$dashboard_page_id = get_option('mvbs_vendor_dashboard_page_id');
				if ($dashboard_page_id) {
					$dashboard_url = get_permalink($dashboard_page_id);
					return '<p>' . sprintf(__('You are already logged in. <a href="%s">Go to Dashboard</a>', 'multi-vender-book-store'), $dashboard_url) . '</p>';
				}
			}
		}

		// Handle form submission
		if (isset($_POST['mvbs_vendor_login'])) {
			$result = Multi_Vender_Book_Store_Vendor::authenticate_vendor($_POST['username'], $_POST['password']);

			if ($result['success']) {
				$redirect_url = !empty($atts['redirect']) ? $atts['redirect'] : get_permalink(get_option('mvbs_vendor_dashboard_page_id'));
				wp_redirect($redirect_url);
				exit;
			} else {
				$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
			}
		}

		ob_start();
		?>
		<div class="mvbs-vendor-login">
			<div class="mvbs-login-header">
				<h2><?php _e('Vendor Login', 'multi-vender-book-store'); ?></h2>
				<p class="mvbs-login-subtitle"><?php _e('Access your vendor dashboard', 'multi-vender-book-store'); ?></p>
			</div>

			<?php if (isset($error_message)) echo $error_message; ?>

			<form method="post" action="" class="mvbs-login-form">
				<?php wp_nonce_field('mvbs_vendor_login', 'mvbs_nonce'); ?>

				<div class="mvbs-form-group">
					<label for="username"><?php _e('Username or Email', 'multi-vender-book-store'); ?></label>
					<input type="text" name="username" id="username" required value="<?php echo esc_attr($_POST['username'] ?? ''); ?>" placeholder="<?php _e('Enter your username or email', 'multi-vender-book-store'); ?>" />
				</div>

				<div class="mvbs-form-group">
					<label for="password"><?php _e('Password', 'multi-vender-book-store'); ?></label>
					<input type="password" name="password" id="password" required placeholder="<?php _e('Enter your password', 'multi-vender-book-store'); ?>" />
				</div>

				<div class="mvbs-form-actions">
					<button type="submit" name="mvbs_vendor_login" class="mvbs-btn mvbs-btn-primary mvbs-btn-large">
						<span class="mvbs-btn-icon">🔑</span>
						<?php _e('Sign In', 'multi-vender-book-store'); ?>
					</button>
				</div>

				<div class="mvbs-form-footer">
					<p><a href="<?php echo wp_lostpassword_url(); ?>" class="mvbs-link"><?php _e('Forgot your password?', 'multi-vender-book-store'); ?></a></p>
					<p><?php _e('Don\'t have an account?', 'multi-vender-book-store'); ?>
					<a href="<?php echo get_permalink(get_option('mvbs_vendor_registration_page_id')); ?>" class="mvbs-link"><?php _e('Register here', 'multi-vender-book-store'); ?></a></p>
				</div>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Vendor dashboard shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Dashboard HTML.
	 */
	public function vendor_dashboard_shortcode($atts) {
		// Check if admin is viewing a specific vendor's dashboard
		$admin_viewing = false;
		$vendor = null;

		if (isset($_GET['mvbs_admin_view']) && $_GET['mvbs_admin_view'] === 'vendor_dashboard' && isset($_GET['vendor_id'])) {
			if (current_user_can('manage_vendors')) {
				$vendor_id = intval($_GET['vendor_id']);
				// Get vendor by vendor ID directly
				global $wpdb;
				$table_name = $wpdb->prefix . 'mvbs_vendors';
				$vendor = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $vendor_id));
				if ($vendor) {
					$admin_viewing = true;
				}
			}
		}

		if (!$admin_viewing) {
			$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

			if (!$vendor) {
				$login_page_id = get_option('mvbs_vendor_login_page_id');
				$login_url = $login_page_id ? get_permalink($login_page_id) : wp_login_url();
				return '<p>' . sprintf(__('Please <a href="%s">login</a> to access your vendor dashboard.', 'multi-vender-book-store'), $login_url) . '</p>';
			}
		}

		if ($vendor->status !== 'approved') {
			return '<div class="mvbs-pending-approval">
				<div class="mvbs-icon">⏳</div>
				<h3>' . __('Account Pending Approval', 'multi-vender-book-store') . '</h3>
				<p>' . __('Your vendor account is currently under review. You will receive an email notification once your account is approved.', 'multi-vender-book-store') . '</p>
			</div>';
		}

		// Handle different dashboard actions (disable for admin viewing)
		$action = isset($_GET['action']) && !$admin_viewing ? sanitize_text_field($_GET['action']) : 'dashboard';

		if (!$admin_viewing) {
			switch ($action) {
				case 'add_product':
					return $this->add_product_shortcode($atts);
				case 'manage_products':
					return $this->manage_products_shortcode($atts);
				case 'edit_product':
					return $this->edit_product_shortcode($atts);
				case 'view_orders':
					return $this->vendor_orders_shortcode($atts);
				case 'profile':
					return $this->vendor_profile_shortcode($atts);
				case 'messages':
					return $this->messaging_shortcode($atts);
				default:
					return $this->render_dashboard_home($vendor, $admin_viewing);
			}
		} else {
			return $this->render_dashboard_home($vendor, $admin_viewing);
		}
	}

	/**
	 * Render dashboard home view.
	 *
	 * @since    1.0.0
	 * @param    object $vendor        Vendor data.
	 * @param    bool   $admin_viewing Whether admin is viewing.
	 * @return   string                Dashboard HTML.
	 */
	private function render_dashboard_home($vendor, $admin_viewing = false) {
		$stats = Multi_Vender_Book_Store_Database::get_vendor_stats($vendor->id);

		// Add body class to hide WordPress elements
		add_filter('body_class', function($classes) {
			$classes[] = 'mvbs-dashboard-active';
			return $classes;
		});

		ob_start();
		?>
		<div class="mvbs-vendor-dashboard">
			<!-- Mobile Overlay -->
			<div class="mvbs-mobile-overlay" id="mobileOverlay"></div>

			<!-- Mobile Header -->
			<div class="mvbs-mobile-header">
				<button class="mvbs-mobile-menu-btn" onclick="toggleSidebar()">☰</button>
				<div class="mvbs-mobile-title">
					<strong><?php echo esc_html($vendor->business_name); ?></strong>
				</div>
			</div>

			<!-- Sidebar Navigation -->
			<div class="mvbs-dashboard-sidebar" id="dashboardSidebar">
				<div class="mvbs-sidebar-header">
					<!-- Website Logo -->
					<a href="<?php echo esc_url(home_url('/')); ?>" class="mvbs-website-logo">
						<span class="mvbs-home-icon">🏠</span>
						<span><?php echo esc_html(get_bloginfo('name')); ?></span>
					</a>

					<!-- Vendor Info -->
					<div class="mvbs-sidebar-logo">
						<div class="mvbs-vendor-info">
							<span class="mvbs-logo-icon">🏪</span>
							<span><?php echo esc_html($vendor->business_name); ?></span>
						</div>
						<button class="mvbs-sidebar-toggle" onclick="toggleSidebar()">⟨</button>
					</div>
				</div>

				<nav class="mvbs-sidebar-nav">
					<a href="?action=dashboard" class="mvbs-nav-item <?php echo (!isset($_GET['action']) || $_GET['action'] === 'dashboard') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">📊</span>
						<span class="mvbs-nav-text"><?php _e('Dashboard', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=add_product" class="mvbs-nav-item <?php echo (isset($_GET['action']) && $_GET['action'] === 'add_product') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">📚</span>
						<span class="mvbs-nav-text"><?php _e('Add Book', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=manage_products" class="mvbs-nav-item <?php echo (isset($_GET['action']) && $_GET['action'] === 'manage_products') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">📖</span>
						<span class="mvbs-nav-text"><?php _e('Manage Books', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=view_orders" class="mvbs-nav-item <?php echo (isset($_GET['action']) && $_GET['action'] === 'view_orders') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">📦</span>
						<span class="mvbs-nav-text"><?php _e('Orders', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=messages" class="mvbs-nav-item <?php echo (isset($_GET['action']) && $_GET['action'] === 'messages') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">💬</span>
						<span class="mvbs-nav-text"><?php _e('Messages', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=profile" class="mvbs-nav-item <?php echo (isset($_GET['action']) && $_GET['action'] === 'profile') ? 'active' : ''; ?>">
						<span class="mvbs-nav-icon">👤</span>
						<span class="mvbs-nav-text"><?php _e('Profile', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="<?php echo wp_logout_url(get_permalink()); ?>" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">🚪</span>
						<span class="mvbs-nav-text"><?php _e('Logout', 'multi-vender-book-store'); ?></span>
					</a>
				</nav>
			</div>

			<!-- Mobile Overlay -->
			<div class="mvbs-mobile-overlay" id="mobileOverlay" onclick="closeSidebar()"></div>

			<!-- Main Content -->
			<div class="mvbs-dashboard-main">
				<div class="mvbs-dashboard-content">
					<?php if ($admin_viewing): ?>
						<div class="mvbs-admin-viewing-notice">
							<span class="mvbs-admin-badge">👑 Admin View</span>
							<p><?php _e('You are viewing this vendor\'s dashboard as an administrator.', 'multi-vender-book-store'); ?></p>
						</div>
					<?php endif; ?>

					<!-- Dashboard Header -->
					<div class="mvbs-dashboard-header">
						<div>
							<h2>
								<span class="mvbs-title-icon">📊</span>
								<?php _e('Dashboard Overview', 'multi-vender-book-store'); ?>
							</h2>
							<p class="mvbs-dashboard-subtitle"><?php _e('Manage your products and track your sales', 'multi-vender-book-store'); ?></p>
						</div>
						<div class="mvbs-dashboard-header-actions">
							<div class="mvbs-user-menu">
								<div class="mvbs-user-avatar"><?php echo strtoupper(substr($vendor->business_name, 0, 1)); ?></div>
								<span><?php echo esc_html($vendor->business_name); ?></span>
							</div>
						</div>
					</div>

			<div class="mvbs-dashboard-stats">
				<div class="mvbs-stat-card mvbs-stat-primary">
					<div class="mvbs-stat-icon">📚</div>
					<div class="mvbs-stat-content">
						<h3><?php _e('Total Products', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-stat-number"><?php echo intval($stats['total_products']); ?></p>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-success">
					<div class="mvbs-stat-icon">✅</div>
					<div class="mvbs-stat-content">
						<h3><?php _e('Approved Products', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-stat-number"><?php echo intval($stats['approved_products']); ?></p>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-info">
					<div class="mvbs-stat-icon">💰</div>
					<div class="mvbs-stat-content">
						<h3><?php _e('Total Earnings', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-stat-number">$<?php echo number_format($stats['total_earnings'], 2); ?></p>
					</div>
				</div>

				<div class="mvbs-stat-card mvbs-stat-warning">
					<div class="mvbs-stat-icon">⏱️</div>
					<div class="mvbs-stat-content">
						<h3><?php _e('Pending Earnings', 'multi-vender-book-store'); ?></h3>
						<p class="mvbs-stat-number">$<?php echo number_format($stats['pending_earnings'], 2); ?></p>
					</div>
				</div>
			</div>

			<div class="mvbs-dashboard-actions">
				<a href="?action=add_product" class="mvbs-btn mvbs-btn-primary">
					<span class="mvbs-btn-icon">📚</span>
					<?php _e('Add New Book', 'multi-vender-book-store'); ?>
				</a>
				<a href="?action=manage_products" class="mvbs-btn mvbs-btn-secondary">
					<span class="mvbs-btn-icon">📖</span>
					<?php _e('Manage Books', 'multi-vender-book-store'); ?>
				</a>
				<a href="?action=view_orders" class="mvbs-btn mvbs-btn-secondary">
					<span class="mvbs-btn-icon">📦</span>
					<?php _e('View Orders', 'multi-vender-book-store'); ?>
				</a>
				<a href="?action=messages" class="mvbs-btn mvbs-btn-secondary">
					<span class="mvbs-btn-icon">💬</span>
					<?php _e('Messages', 'multi-vender-book-store'); ?>
				</a>
				<a href="?action=profile" class="mvbs-btn mvbs-btn-secondary">
					<span class="mvbs-btn-icon">👤</span>
					<?php _e('Edit Profile', 'multi-vender-book-store'); ?>
				</a>
				<a href="<?php echo wp_logout_url(); ?>" class="mvbs-btn mvbs-btn-outline">
					<span class="mvbs-btn-icon">🚪</span>
					<?php _e('Logout', 'multi-vender-book-store'); ?>
				</a>
			</div>

					<!-- Recent Activity Section -->
					<div class="mvbs-dashboard-section">
						<div class="mvbs-section-header">
							<h3 class="mvbs-section-title">
								<span>📈</span>
								<?php _e('Recent Activity', 'multi-vender-book-store'); ?>
							</h3>
						</div>
						<div class="mvbs-section-content">
							<div class="mvbs-activity-list">
								<p class="mvbs-no-activity"><?php _e('No recent activity to display.', 'multi-vender-book-store'); ?></p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Dashboard JavaScript -->
		<script>
		function toggleSidebar() {
			const sidebar = document.getElementById('dashboardSidebar');
			const overlay = document.getElementById('mobileOverlay');

			if (window.innerWidth <= 1024) {
				// Mobile behavior
				sidebar.classList.toggle('mobile-open');
				overlay.classList.toggle('active');
			} else {
				// Desktop behavior
				sidebar.classList.toggle('collapsed');
			}
		}

		function closeSidebar() {
			const sidebar = document.getElementById('dashboardSidebar');
			const overlay = document.getElementById('mobileOverlay');

			sidebar.classList.remove('mobile-open');
			overlay.classList.remove('active');
		}

		// Close sidebar on window resize if mobile
		window.addEventListener('resize', function() {
			if (window.innerWidth > 1024) {
				const sidebar = document.getElementById('dashboardSidebar');
				const overlay = document.getElementById('mobileOverlay');
				sidebar.classList.remove('mobile-open');
				overlay.classList.remove('active');
			}
		});
		</script>
		<?php
		return ob_get_clean();
	}

	/**
	 * Add product shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Add product form HTML.
	 */
	public function add_product_shortcode($atts) {
		$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

		if (!$vendor || $vendor->status !== 'approved') {
			return '<p>' . __('Access denied.', 'multi-vender-book-store') . '</p>';
		}

		// Handle form submission
		if (isset($_POST['mvbs_add_product'])) {
			if (!wp_verify_nonce($_POST['mvbs_nonce'], 'mvbs_add_product')) {
				$error_message = '<div class="mvbs-error">' . __('Security check failed.', 'multi-vender-book-store') . '</div>';
			} else {
				$result = Multi_Vender_Book_Store_Product::create_vendor_product($_POST, $vendor->id);

				if ($result['success']) {
					return '<div class="mvbs-success">
						<div class="mvbs-success-icon">✅</div>
						<h3>' . __('Product Added Successfully!', 'multi-vender-book-store') . '</h3>
						<p>' . esc_html($result['message']) . '</p>
						<a href="?action=manage_products" class="mvbs-btn mvbs-btn-primary">' . __('View My Products', 'multi-vender-book-store') . '</a>
						<a href="?" class="mvbs-btn mvbs-btn-secondary">' . __('Back to Dashboard', 'multi-vender-book-store') . '</a>
					</div>';
				} else {
					$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
				}
			}
		}

		ob_start();
		?>
		<div class="mvbs-vendor-dashboard">
			<!-- Mobile Overlay -->
			<div class="mvbs-mobile-overlay" id="mobileOverlay"></div>

			<!-- Mobile Header -->
			<div class="mvbs-mobile-header">
				<button class="mvbs-mobile-menu-btn" onclick="toggleSidebar()">☰</button>
				<div class="mvbs-mobile-title">
					<strong><?php _e('Add Book', 'multi-vender-book-store'); ?></strong>
				</div>
			</div>

			<!-- Sidebar Navigation -->
			<div class="mvbs-dashboard-sidebar" id="dashboardSidebar">
				<div class="mvbs-sidebar-header">
					<!-- Website Logo -->
					<a href="<?php echo esc_url(home_url('/')); ?>" class="mvbs-website-logo">
						<span class="mvbs-home-icon">🏠</span>
						<span><?php echo esc_html(get_bloginfo('name')); ?></span>
					</a>

					<!-- Vendor Info -->
					<div class="mvbs-sidebar-logo">
						<div class="mvbs-vendor-info">
							<span class="mvbs-logo-icon">🏪</span>
							<span><?php echo esc_html($vendor->business_name); ?></span>
						</div>
						<button class="mvbs-sidebar-toggle" onclick="toggleSidebar()">⟨</button>
					</div>
				</div>

				<nav class="mvbs-sidebar-nav">
					<a href="?action=dashboard" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">📊</span>
						<span class="mvbs-nav-text"><?php _e('Dashboard', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=add_product" class="mvbs-nav-item active">
						<span class="mvbs-nav-icon">📚</span>
						<span class="mvbs-nav-text"><?php _e('Add Book', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=manage_products" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">📖</span>
						<span class="mvbs-nav-text"><?php _e('Manage Books', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=view_orders" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">📦</span>
						<span class="mvbs-nav-text"><?php _e('Orders', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=messages" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">💬</span>
						<span class="mvbs-nav-text"><?php _e('Messages', 'multi-vender-book-store'); ?></span>
					</a>
					<a href="?action=profile" class="mvbs-nav-item">
						<span class="mvbs-nav-icon">👤</span>
						<span class="mvbs-nav-text"><?php _e('Profile', 'multi-vender-book-store'); ?></span>
					</a>
				</nav>
			</div>

			<!-- Main Content -->
			<div class="mvbs-dashboard-main">
				<div class="mvbs-dashboard-content">
					<!-- Dashboard Header -->
					<div class="mvbs-dashboard-header">
						<div>
							<h2>
								<span class="mvbs-title-icon">📚</span>
								<?php _e('Add New Book', 'multi-vender-book-store'); ?>
							</h2>
							<p class="mvbs-dashboard-subtitle"><?php _e('List your book for sale on the marketplace', 'multi-vender-book-store'); ?></p>
						</div>
						<div class="mvbs-dashboard-header-actions">
							<a href="?" class="mvbs-btn mvbs-btn-outline">
								<span class="mvbs-btn-icon">←</span>
								<?php _e('Back to Dashboard', 'multi-vender-book-store'); ?>
							</a>
						</div>
					</div>

					<?php if (isset($error_message)) echo $error_message; ?>

					<form method="post" action="" enctype="multipart/form-data" class="mvbs-book-form">
						<?php wp_nonce_field('mvbs_add_product', 'mvbs_nonce'); ?>

							<!-- Basic Information Section -->
							<div class="mvbs-form-section">
								<div class="mvbs-section-header">
									<h3><span class="mvbs-section-icon">📝</span><?php _e('Basic Information', 'multi-vender-book-store'); ?></h3>
									<p class="mvbs-section-subtitle"><?php _e('Essential details about your book', 'multi-vender-book-store'); ?></p>
								</div>

								<div class="mvbs-form-group mvbs-form-group-full">
									<label for="title" class="mvbs-required"><?php _e('Title / Titill', 'multi-vender-book-store'); ?></label>
									<input type="text" name="title" id="title" required value="<?php echo esc_attr($_POST['title'] ?? ''); ?>" placeholder="<?php _e('Enter the book title', 'multi-vender-book-store'); ?>" />
								</div>

								<div class="mvbs-form-row">
									<div class="mvbs-form-group">
										<label for="author" class="mvbs-required"><?php _e('Author(s) / Höfundur', 'multi-vender-book-store'); ?></label>
										<input type="text" name="author" id="author" required value="<?php echo esc_attr($_POST['author'] ?? ''); ?>" placeholder="<?php _e('e.g. John Doe, Jane Smith', 'multi-vender-book-store'); ?>" />
									</div>
									<div class="mvbs-form-group">
										<label for="edition"><?php _e('Edition / Útgáfa', 'multi-vender-book-store'); ?></label>
										<input type="text" name="edition" id="edition" value="<?php echo esc_attr($_POST['edition'] ?? ''); ?>" placeholder="<?php _e('e.g. 2nd Edition', 'multi-vender-book-store'); ?>" />
									</div>
								</div>

								<div class="mvbs-form-group">
									<label for="publisher"><?php _e('Publisher / Útgefandi', 'multi-vender-book-store'); ?></label>
									<input type="text" name="publisher" id="publisher" value="<?php echo esc_attr($_POST['publisher'] ?? ''); ?>" placeholder="<?php _e('Enter publisher name', 'multi-vender-book-store'); ?>" />
								</div>

								<div class="mvbs-form-group mvbs-form-group-full">
									<label for="description"><?php _e('Description / Lýsing', 'multi-vender-book-store'); ?></label>
									<textarea name="description" id="description" rows="4" maxlength="500" placeholder="<?php _e('Brief description of the book (max 100 words)', 'multi-vender-book-store'); ?>"><?php echo esc_textarea($_POST['description'] ?? ''); ?></textarea>
									<div class="mvbs-char-counter">
										<span id="desc-counter">0</span> / 500 <?php _e('characters', 'multi-vender-book-store'); ?>
									</div>
								</div>
							</div>

							<!-- Pricing & Condition Section -->
							<div class="mvbs-form-section">
								<div class="mvbs-section-header">
									<h3><span class="mvbs-section-icon">💰</span><?php _e('Pricing & Condition', 'multi-vender-book-store'); ?></h3>
									<p class="mvbs-section-subtitle"><?php _e('Set your price and book condition', 'multi-vender-book-store'); ?></p>
								</div>

								<div class="mvbs-form-row">
									<div class="mvbs-form-group">
										<label for="price" class="mvbs-required"><?php _e('Price / Verð (ISK)', 'multi-vender-book-store'); ?></label>
										<div class="mvbs-price-input">
											<input type="number" step="1" name="price" id="price" required value="<?php echo esc_attr($_POST['price'] ?? ''); ?>" min="500" placeholder="500" />
											<span class="mvbs-currency">ISK</span>
										</div>
										<div class="mvbs-field-note"><?php _e('Minimum price is 500 ISK', 'multi-vender-book-store'); ?></div>
									</div>
									<div class="mvbs-form-group">
										<label for="condition" class="mvbs-required"><?php _e('Condition / Ástand', 'multi-vender-book-store'); ?></label>
										<select name="condition" id="condition" required>
											<option value=""><?php _e('Select condition', 'multi-vender-book-store'); ?></option>
											<option value="new" <?php selected($_POST['condition'] ?? '', 'new'); ?>><?php _e('New / Ný', 'multi-vender-book-store'); ?></option>
											<option value="like_new" <?php selected($_POST['condition'] ?? '', 'like_new'); ?>><?php _e('Like new / Eins og ný', 'multi-vender-book-store'); ?></option>
											<option value="used" <?php selected($_POST['condition'] ?? '', 'used'); ?>><?php _e('Used / Notuð', 'multi-vender-book-store'); ?></option>
										</select>
									</div>
								</div>
							</div>

							<!-- Category Section -->
							<div class="mvbs-form-section">
								<div class="mvbs-section-header">
									<h3><span class="mvbs-section-icon">📂</span><?php _e('Category / Flokkur', 'multi-vender-book-store'); ?></h3>
									<p class="mvbs-section-subtitle"><?php _e('Choose the most appropriate category for your book', 'multi-vender-book-store'); ?></p>
								</div>

								<div class="mvbs-form-group">
									<label for="category" class="mvbs-required"><?php _e('Book Category', 'multi-vender-book-store'); ?></label>
									<select name="category" id="category" required>
										<option value=""><?php _e('Select category', 'multi-vender-book-store'); ?></option>
										<option value="academic" <?php selected($_POST['category'] ?? '', 'academic'); ?>><?php _e('Academic / Námsbækur', 'multi-vender-book-store'); ?></option>
										<option value="fiction" <?php selected($_POST['category'] ?? '', 'fiction'); ?>><?php _e('Fiction / Skáldskapur', 'multi-vender-book-store'); ?></option>
										<option value="non_fiction" <?php selected($_POST['category'] ?? '', 'non_fiction'); ?>><?php _e('Non-Fiction / Fræðirit', 'multi-vender-book-store'); ?></option>
										<option value="sci_fi" <?php selected($_POST['category'] ?? '', 'sci_fi'); ?>><?php _e('Sci-Fi / Vísindaskáldskapur', 'multi-vender-book-store'); ?></option>
										<option value="fantasy" <?php selected($_POST['category'] ?? '', 'fantasy'); ?>><?php _e('Fantasy / Fantasía', 'multi-vender-book-store'); ?></option>
										<option value="childrens" <?php selected($_POST['category'] ?? '', 'childrens'); ?>><?php _e('Children\'s / Barnabækur', 'multi-vender-book-store'); ?></option>
										<option value="self_help" <?php selected($_POST['category'] ?? '', 'self_help'); ?>><?php _e('Self help and personal development / Sjálfshjálp', 'multi-vender-book-store'); ?></option>
										<option value="other" <?php selected($_POST['category'] ?? '', 'other'); ?>><?php _e('Other / Annað', 'multi-vender-book-store'); ?></option>
									</select>
								</div>

								<div class="mvbs-form-group" id="academic-subcategory" style="display: none;">
									<label for="academic_subcategory"><?php _e('Academic Field', 'multi-vender-book-store'); ?></label>
									<select name="academic_subcategory" id="academic_subcategory">
										<option value=""><?php _e('Select field of study', 'multi-vender-book-store'); ?></option>
										<option value="mathematics" <?php selected($_POST['academic_subcategory'] ?? '', 'mathematics'); ?>><?php _e('Mathematics / Stærðfræði', 'multi-vender-book-store'); ?></option>
										<option value="physics" <?php selected($_POST['academic_subcategory'] ?? '', 'physics'); ?>><?php _e('Physics / Eðlisfræði', 'multi-vender-book-store'); ?></option>
										<option value="business" <?php selected($_POST['academic_subcategory'] ?? '', 'business'); ?>><?php _e('Business / Viðskiptafræði', 'multi-vender-book-store'); ?></option>
										<option value="chemistry" <?php selected($_POST['academic_subcategory'] ?? '', 'chemistry'); ?>><?php _e('Chemistry / Efnafræði', 'multi-vender-book-store'); ?></option>
										<option value="biology" <?php selected($_POST['academic_subcategory'] ?? '', 'biology'); ?>><?php _e('Biology / Líffræði', 'multi-vender-book-store'); ?></option>
										<option value="computer_science" <?php selected($_POST['academic_subcategory'] ?? '', 'computer_science'); ?>><?php _e('Computer science / Tölvunarfræði', 'multi-vender-book-store'); ?></option>
										<option value="law" <?php selected($_POST['academic_subcategory'] ?? '', 'law'); ?>><?php _e('Law / Lögfræði', 'multi-vender-book-store'); ?></option>
										<option value="social_sciences" <?php selected($_POST['academic_subcategory'] ?? '', 'social_sciences'); ?>><?php _e('Social sciences / Félagsvísindi', 'multi-vender-book-store'); ?></option>
									</select>
								</div>
							</div>

							<!-- Delivery Options Section -->
							<div class="mvbs-form-section">
								<div class="mvbs-section-header">
									<h3><span class="mvbs-section-icon">🚚</span><?php _e('Delivery and pickup options / Afhendingarmáti', 'multi-vender-book-store'); ?></h3>
									<p class="mvbs-section-subtitle"><?php _e('Choose how buyers can receive the book (at least one option required)', 'multi-vender-book-store'); ?></p>
								</div>

								<div class="mvbs-delivery-options">
									<div class="mvbs-checkbox-group">
										<label class="mvbs-checkbox-label">
											<input type="checkbox" name="delivery_options[]" value="home_pickup" <?php checked(in_array('home_pickup', $_POST['delivery_options'] ?? [])); ?> />
											<span class="mvbs-checkbox-custom"></span>
											<div class="mvbs-checkbox-content">
												<strong><?php _e('Home pickup allowed / Sækja heim', 'multi-vender-book-store'); ?></strong>
												<p><?php _e('Buyer can arrange to pick up the book from your location', 'multi-vender-book-store'); ?></p>
											</div>
										</label>
									</div>

									<div class="mvbs-checkbox-group">
										<label class="mvbs-checkbox-label">
											<input type="checkbox" name="delivery_options[]" value="dropp_delivery" <?php checked(in_array('dropp_delivery', $_POST['delivery_options'] ?? [])); ?> />
											<span class="mvbs-checkbox-custom"></span>
											<div class="mvbs-checkbox-content">
												<strong><?php _e('Delivery via Dropp / Afhending með Dropp', 'multi-vender-book-store'); ?></strong>
												<p><?php _e('Book will be delivered using Dropp delivery service', 'multi-vender-book-store'); ?></p>
											</div>
										</label>
									</div>
								</div>
								<div class="mvbs-field-note mvbs-required-note"><?php _e('At least one delivery option must be selected', 'multi-vender-book-store'); ?></div>
							</div>

							<!-- Images Section -->
							<div class="mvbs-form-section">
								<div class="mvbs-section-header">
									<h3><span class="mvbs-section-icon">📸</span><?php _e('Book Images', 'multi-vender-book-store'); ?></h3>
									<p class="mvbs-section-subtitle"><?php _e('Upload clear photos of your book (front and back recommended)', 'multi-vender-book-store'); ?></p>
								</div>

								<div class="mvbs-form-group mvbs-form-group-full">
									<label for="product_images" class="mvbs-required"><?php _e('Book Photos', 'multi-vender-book-store'); ?></label>
									<div class="mvbs-file-upload">
										<input type="file" name="product_images[]" id="product_images" multiple accept="image/*" required />
										<div class="mvbs-upload-area">
											<span class="mvbs-upload-icon">📷</span>
											<p><strong><?php _e('Click to upload images', 'multi-vender-book-store'); ?></strong></p>
											<p><?php _e('or drag and drop files here', 'multi-vender-book-store'); ?></p>
										</div>
									</div>
									<div class="mvbs-field-note"><?php _e('Upload at least 2 images (front and back). Maximum 5 images. First image will be the main photo.', 'multi-vender-book-store'); ?></div>
								</div>
							</div>

							<!-- Form Actions -->
							<div class="mvbs-form-actions">
								<button type="submit" name="mvbs_add_product" class="mvbs-btn mvbs-btn-primary mvbs-btn-large">
									<span class="mvbs-btn-icon">📚</span>
									<?php _e('Add Book to Marketplace', 'multi-vender-book-store'); ?>
								</button>
								<a href="?" class="mvbs-btn mvbs-btn-secondary mvbs-btn-large">
									<span class="mvbs-btn-icon">←</span>
									<?php _e('Cancel', 'multi-vender-book-store'); ?>
								</a>
							</div>
						</form>
					</div>
				</div>
			</div>

			<script>
			// Category subcategory toggle
			document.getElementById('category').addEventListener('change', function() {
				const subcategoryDiv = document.getElementById('academic-subcategory');
				if (this.value === 'academic') {
					subcategoryDiv.style.display = 'block';
					document.getElementById('academic_subcategory').required = true;
				} else {
					subcategoryDiv.style.display = 'none';
					document.getElementById('academic_subcategory').required = false;
					document.getElementById('academic_subcategory').value = '';
				}
			});

			// Character counter for description
			const descTextarea = document.getElementById('description');
			const descCounter = document.getElementById('desc-counter');
			if (descTextarea && descCounter) {
				descTextarea.addEventListener('input', function() {
					descCounter.textContent = this.value.length;
				});
				// Initialize counter
				descCounter.textContent = descTextarea.value.length;
			}

			// Price validation
			document.getElementById('price').addEventListener('input', function() {
				const price = parseFloat(this.value);
				if (price < 500 && price > 0) {
					this.setCustomValidity('<?php _e('Minimum price is 500 ISK', 'multi-vender-book-store'); ?>');
				} else {
					this.setCustomValidity('');
				}
			});

			// Delivery options validation
			const deliveryCheckboxes = document.querySelectorAll('input[name="delivery_options[]"]');
			function validateDeliveryOptions() {
				const checked = Array.from(deliveryCheckboxes).some(cb => cb.checked);
				deliveryCheckboxes.forEach(cb => {
					if (!checked) {
						cb.setCustomValidity('<?php _e('Please select at least one delivery option', 'multi-vender-book-store'); ?>');
					} else {
						cb.setCustomValidity('');
					}
				});
			}

			deliveryCheckboxes.forEach(cb => {
				cb.addEventListener('change', validateDeliveryOptions);
			});

			// Initialize on page load
			document.addEventListener('DOMContentLoaded', function() {
				// Trigger category change if academic is selected
				if (document.getElementById('category').value === 'academic') {
					document.getElementById('category').dispatchEvent(new Event('change'));
				}
			});
			</script>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Manage products shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Products management HTML.
	 */
	public function manage_products_shortcode($atts) {
		$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

		if (!$vendor || $vendor->status !== 'approved') {
			return '<p>' . __('Access denied.', 'multi-vender-book-store') . '</p>';
		}

		$paged = isset($_GET['paged']) ? intval($_GET['paged']) : 1;
		$products = Multi_Vender_Book_Store_Product::get_vendor_products($vendor->id, array(
			'posts_per_page' => 10,
			'paged' => $paged
		));

		ob_start();
		?>
		<div class="mvbs-manage-products">
			<div class="mvbs-page-header">
				<h2><?php _e('Manage Products', 'multi-vender-book-store'); ?></h2>
				<div class="mvbs-page-actions">
					<a href="?action=add_product" class="mvbs-btn mvbs-btn-primary">
						<span class="mvbs-btn-icon">➕</span>
						<?php _e('Add New Product', 'multi-vender-book-store'); ?>
					</a>
					<a href="?" class="mvbs-btn mvbs-btn-outline">← <?php _e('Back to Dashboard', 'multi-vender-book-store'); ?></a>
				</div>
			</div>

			<?php if (empty($products)): ?>
				<div class="mvbs-empty-state">
					<div class="mvbs-empty-icon">📚</div>
					<h3><?php _e('No Books Yet', 'multi-vender-book-store'); ?></h3>
					<p><?php _e('You haven\'t added any books yet. Start by adding your first book!', 'multi-vender-book-store'); ?></p>
					<a href="?action=add_product" class="mvbs-btn mvbs-btn-primary"><?php _e('Add Your First Book', 'multi-vender-book-store'); ?></a>
				</div>
			<?php else: ?>
				<div class="mvbs-products-grid">
					<?php foreach ($products as $product): ?>
						<div class="mvbs-product-card">
							<div class="mvbs-product-image">
								<?php if (has_post_thumbnail($product->ID)): ?>
									<?php echo get_the_post_thumbnail($product->ID, 'medium'); ?>
								<?php else: ?>
									<div class="mvbs-no-image">📖</div>
								<?php endif; ?>
							</div>

							<div class="mvbs-product-info">
								<h4><?php echo esc_html($product->post_title); ?></h4>
								<p class="mvbs-product-price">$<?php echo number_format(get_post_meta($product->ID, '_regular_price', true), 2); ?></p>
								<p class="mvbs-product-condition"><?php echo esc_html(get_post_meta($product->ID, 'book_condition', true)); ?></p>

								<div class="mvbs-product-status">
									<?php
									$status_class = 'mvbs-status-' . $product->vendor_status;
									$status_text = '';
									switch ($product->vendor_status) {
										case 'approved':
											$status_text = __('Published', 'multi-vender-book-store');
											break;
										case 'pending':
											$status_text = __('Pending Review', 'multi-vender-book-store');
											break;
										case 'rejected':
											$status_text = __('Rejected', 'multi-vender-book-store');
											break;
										default:
											$status_text = __('Draft', 'multi-vender-book-store');
									}
									?>
									<span class="mvbs-status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
								</div>

								<div class="mvbs-product-actions">
									<a href="?action=edit_product&product_id=<?php echo $product->ID; ?>" class="mvbs-btn mvbs-btn-small mvbs-btn-secondary"><?php _e('Edit', 'multi-vender-book-store'); ?></a>
									<a href="#" class="mvbs-btn mvbs-btn-small mvbs-btn-outline" onclick="return confirm('<?php _e('Are you sure you want to delete this product?', 'multi-vender-book-store'); ?>')"><?php _e('Delete', 'multi-vender-book-store'); ?></a>
								</div>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Edit product shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Edit product form HTML.
	 */
	public function edit_product_shortcode($atts) {
		$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

		if (!$vendor || $vendor->status !== 'approved') {
			return '<p>' . __('Access denied.', 'multi-vender-book-store') . '</p>';
		}

		$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

		if (!$product_id || !Multi_Vender_Book_Store_Product::vendor_owns_product($vendor->id, $product_id)) {
			return '<p>' . __('Product not found or access denied.', 'multi-vender-book-store') . '</p>';
		}

		$product = get_post($product_id);
		if (!$product) {
			return '<p>' . __('Product not found.', 'multi-vender-book-store') . '</p>';
		}

		// Handle form submission
		if (isset($_POST['mvbs_update_product'])) {
			if (!wp_verify_nonce($_POST['mvbs_nonce'], 'mvbs_edit_product')) {
				$error_message = '<div class="mvbs-error">' . __('Security check failed.', 'multi-vender-book-store') . '</div>';
			} else {
				$result = Multi_Vender_Book_Store_Product::update_vendor_product($product_id, $_POST, $vendor->id);

				if ($result['success']) {
					$success_message = '<div class="mvbs-success">' . esc_html($result['message']) . '</div>';
					// Refresh product data
					$product = get_post($product_id);
				} else {
					$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
				}
			}
		}

		// Get product meta
		$meta = get_post_meta($product_id);
		$price = $meta['_regular_price'][0] ?? '';
		$condition = $meta['book_condition'][0] ?? '';
		$isbn = $meta['book_isbn'][0] ?? '';
		$author = $meta['book_author'][0] ?? '';
		$publisher = $meta['book_publisher'][0] ?? '';
		$year = $meta['book_year'][0] ?? '';
		$language = $meta['book_language'][0] ?? '';
		$pages = $meta['book_pages'][0] ?? '';
		$stock = $meta['_stock'][0] ?? '';
		$notes = $meta['vendor_notes'][0] ?? '';

		ob_start();
		?>
		<div class="mvbs-edit-product">
			<div class="mvbs-page-header">
				<h2><?php _e('Edit Product', 'multi-vender-book-store'); ?></h2>
				<a href="?action=manage_products" class="mvbs-btn mvbs-btn-outline mvbs-back-btn">← <?php _e('Back to Products', 'multi-vender-book-store'); ?></a>
			</div>

			<?php if (isset($success_message)) echo $success_message; ?>
			<?php if (isset($error_message)) echo $error_message; ?>

			<form method="post" action="" enctype="multipart/form-data" class="mvbs-product-form">
				<?php wp_nonce_field('mvbs_edit_product', 'mvbs_nonce'); ?>

				<div class="mvbs-form-section">
					<h3><?php _e('Basic Information', 'multi-vender-book-store'); ?></h3>

					<div class="mvbs-form-group mvbs-form-group-full">
						<label for="title"><?php _e('Book Title *', 'multi-vender-book-store'); ?></label>
						<input type="text" name="title" id="title" required value="<?php echo esc_attr($product->post_title); ?>" />
					</div>

					<div class="mvbs-form-row">
						<div class="mvbs-form-group">
							<label for="author"><?php _e('Author', 'multi-vender-book-store'); ?></label>
							<input type="text" name="author" id="author" value="<?php echo esc_attr($author); ?>" />
						</div>
						<div class="mvbs-form-group">
							<label for="isbn"><?php _e('ISBN', 'multi-vender-book-store'); ?></label>
							<input type="text" name="isbn" id="isbn" value="<?php echo esc_attr($isbn); ?>" />
						</div>
					</div>

					<div class="mvbs-form-group mvbs-form-group-full">
						<label for="description"><?php _e('Description *', 'multi-vender-book-store'); ?></label>
						<textarea name="description" id="description" required rows="5"><?php echo esc_textarea($product->post_content); ?></textarea>
					</div>
				</div>

				<div class="mvbs-form-section">
					<h3><?php _e('Pricing & Condition', 'multi-vender-book-store'); ?></h3>

					<div class="mvbs-form-row">
						<div class="mvbs-form-group">
							<label for="price"><?php _e('Price ($) *', 'multi-vender-book-store'); ?></label>
							<input type="number" step="0.01" name="price" id="price" required value="<?php echo esc_attr($price); ?>" />
						</div>
						<div class="mvbs-form-group">
							<label for="condition"><?php _e('Condition *', 'multi-vender-book-store'); ?></label>
							<select name="condition" id="condition" required>
								<option value=""><?php _e('Select condition', 'multi-vender-book-store'); ?></option>
								<option value="new" <?php selected($condition, 'new'); ?>><?php _e('New', 'multi-vender-book-store'); ?></option>
								<option value="like_new" <?php selected($condition, 'like_new'); ?>><?php _e('Like New', 'multi-vender-book-store'); ?></option>
								<option value="very_good" <?php selected($condition, 'very_good'); ?>><?php _e('Very Good', 'multi-vender-book-store'); ?></option>
								<option value="good" <?php selected($condition, 'good'); ?>><?php _e('Good', 'multi-vender-book-store'); ?></option>
								<option value="acceptable" <?php selected($condition, 'acceptable'); ?>><?php _e('Acceptable', 'multi-vender-book-store'); ?></option>
							</select>
						</div>
					</div>

					<div class="mvbs-form-group">
						<label for="stock_quantity"><?php _e('Quantity Available', 'multi-vender-book-store'); ?></label>
						<input type="number" name="stock_quantity" id="stock_quantity" value="<?php echo esc_attr($stock); ?>" min="1" />
					</div>
				</div>

				<div class="mvbs-form-actions">
					<input type="submit" name="mvbs_update_product" value="<?php _e('Update Product', 'multi-vender-book-store'); ?>" class="mvbs-btn mvbs-btn-primary mvbs-btn-large" />
					<a href="?action=manage_products" class="mvbs-btn mvbs-btn-secondary mvbs-btn-large"><?php _e('Cancel', 'multi-vender-book-store'); ?></a>
				</div>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Vendor profile shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Profile form HTML.
	 */
	public function vendor_profile_shortcode($atts) {
		return '<div class="mvbs-profile"><p>' . __('Profile management coming soon...', 'multi-vender-book-store') . '</p></div>';
	}

	/**
	 * Vendor orders shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Orders HTML.
	 */
	public function vendor_orders_shortcode($atts) {
		$vendor = Multi_Vender_Book_Store_Vendor::get_current_vendor();

		if (!$vendor || $vendor->status !== 'approved') {
			return '<p>' . __('Access denied.', 'multi-vender-book-store') . '</p>';
		}

		$paged = isset($_GET['paged']) ? intval($_GET['paged']) : 1;
		$orders = Multi_Vender_Book_Store_Order::get_vendor_orders($vendor->id, array(
			'limit' => 10,
			'offset' => ($paged - 1) * 10
		));

		ob_start();
		?>
		<div class="mvbs-vendor-orders">
			<div class="mvbs-page-header">
				<h2><?php _e('My Orders', 'multi-vender-book-store'); ?></h2>
				<div class="mvbs-page-actions">
					<a href="?" class="mvbs-btn mvbs-btn-outline">← <?php _e('Back to Dashboard', 'multi-vender-book-store'); ?></a>
				</div>
			</div>

			<?php if (empty($orders)): ?>
				<div class="mvbs-empty-state">
					<div class="mvbs-empty-icon">📦</div>
					<h3><?php _e('No Orders Yet', 'multi-vender-book-store'); ?></h3>
					<p><?php _e('You haven\'t received any orders yet. Keep promoting your products!', 'multi-vender-book-store'); ?></p>
				</div>
			<?php else: ?>
				<div class="mvbs-orders-list">
					<?php foreach ($orders as $order): ?>
						<div class="mvbs-order-card">
							<div class="mvbs-order-header">
								<div class="mvbs-order-info">
									<h4><?php printf(__('Order #%d', 'multi-vender-book-store'), $order->order_id); ?></h4>
									<p class="mvbs-order-date"><?php echo date('M j, Y', strtotime($order->order_date)); ?></p>
								</div>
								<div class="mvbs-order-amount">
									<span class="mvbs-amount">$<?php echo number_format($order->total_amount, 2); ?></span>
									<span class="mvbs-status-badge mvbs-status-<?php echo esc_attr($order->status); ?>">
										<?php echo ucfirst($order->status); ?>
									</span>
								</div>
							</div>

							<div class="mvbs-order-details">
								<?php if (isset($order->customer_name)): ?>
									<p><strong><?php _e('Customer:', 'multi-vender-book-store'); ?></strong> <?php echo esc_html($order->customer_name); ?></p>
								<?php endif; ?>
								<p><strong><?php _e('Items:', 'multi-vender-book-store'); ?></strong> <?php echo intval($order->item_count); ?></p>
								<?php if (isset($order->order_status)): ?>
									<p><strong><?php _e('Order Status:', 'multi-vender-book-store'); ?></strong> <?php echo ucfirst($order->order_status); ?></p>
								<?php endif; ?>
							</div>

							<div class="mvbs-order-actions">
								<a href="?action=view_order&order_id=<?php echo $order->order_id; ?>" class="mvbs-btn mvbs-btn-small mvbs-btn-secondary">
									<?php _e('View Details', 'multi-vender-book-store'); ?>
								</a>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Public vendor profile shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Vendor profile HTML.
	 */
	public function public_vendor_profile_shortcode($atts) {
		$atts = shortcode_atts(array(
			'vendor_id' => 0
		), $atts);

		// Get vendor ID from URL parameter or shortcode attribute
		$vendor_id = isset($_GET['vendor_id']) ? intval($_GET['vendor_id']) : intval($atts['vendor_id']);

		if (!$vendor_id) {
			return '<p>' . __('Vendor not found.', 'multi-vender-book-store') . '</p>';
		}

		// Get vendor data
		global $wpdb;
		$table_name = $wpdb->prefix . 'mvbs_vendors';
		$vendor = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d AND status = 'approved'", $vendor_id));

		if (!$vendor) {
			return '<p>' . __('Vendor not found or not approved.', 'multi-vender-book-store') . '</p>';
		}

		// Get vendor's approved products
		$products = Multi_Vender_Book_Store_Product::get_vendor_products($vendor_id, array(
			'status' => 'approved',
			'posts_per_page' => 20
		));

		// Get vendor statistics
		$stats = Multi_Vender_Book_Store_Database::get_vendor_stats($vendor_id);

		ob_start();
		?>
		<div class="mvbs-public-vendor-profile">
			<div class="mvbs-vendor-header">
				<div class="mvbs-vendor-avatar">
					<span class="mvbs-avatar-icon">🏪</span>
				</div>
				<div class="mvbs-vendor-info">
					<h1><?php echo esc_html($vendor->business_name); ?></h1>
					<p class="mvbs-vendor-email"><?php echo esc_html($vendor->business_email); ?></p>
					<?php if ($vendor->business_phone): ?>
						<p class="mvbs-vendor-phone">📞 <?php echo esc_html($vendor->business_phone); ?></p>
					<?php endif; ?>
					<?php if ($vendor->business_address): ?>
						<p class="mvbs-vendor-address">📍 <?php echo esc_html($vendor->business_address); ?></p>
					<?php endif; ?>
				</div>
				<div class="mvbs-vendor-stats">
					<div class="mvbs-stat-item">
						<span class="mvbs-stat-number"><?php echo intval($stats['approved_products']); ?></span>
						<span class="mvbs-stat-label"><?php _e('Products', 'multi-vender-book-store'); ?></span>
					</div>
					<div class="mvbs-stat-item">
						<span class="mvbs-stat-number"><?php echo date('Y', strtotime($vendor->created_at)); ?></span>
						<span class="mvbs-stat-label"><?php _e('Member Since', 'multi-vender-book-store'); ?></span>
					</div>
				</div>
			</div>

			<div class="mvbs-vendor-products-section">
				<h2><?php _e('Available Books', 'multi-vender-book-store'); ?></h2>

				<?php if (empty($products)): ?>
					<div class="mvbs-empty-state">
						<div class="mvbs-empty-icon">📚</div>
						<h3><?php _e('No Products Available', 'multi-vender-book-store'); ?></h3>
						<p><?php _e('This vendor hasn\'t listed any products yet.', 'multi-vender-book-store'); ?></p>
					</div>
				<?php else: ?>
					<div class="mvbs-public-products-grid">
						<?php foreach ($products as $product): ?>
							<div class="mvbs-public-product-card">
								<div class="mvbs-product-image">
									<?php if (has_post_thumbnail($product->ID)): ?>
										<a href="<?php echo get_permalink($product->ID); ?>">
											<?php echo get_the_post_thumbnail($product->ID, 'medium'); ?>
										</a>
									<?php else: ?>
										<a href="<?php echo get_permalink($product->ID); ?>" class="mvbs-no-image">
											<span>📖</span>
										</a>
									<?php endif; ?>
								</div>

								<div class="mvbs-product-details">
									<h3><a href="<?php echo get_permalink($product->ID); ?>"><?php echo esc_html($product->post_title); ?></a></h3>

									<div class="mvbs-product-meta">
										<?php
										$author = get_post_meta($product->ID, 'book_author', true);
										$condition = get_post_meta($product->ID, 'book_condition', true);
										$price = get_post_meta($product->ID, '_regular_price', true);
										?>

										<?php if ($author): ?>
											<p class="mvbs-product-author"><?php _e('by', 'multi-vender-book-store'); ?> <?php echo esc_html($author); ?></p>
										<?php endif; ?>

										<p class="mvbs-product-condition"><?php _e('Condition:', 'multi-vender-book-store'); ?> <?php echo ucfirst(str_replace('_', ' ', $condition)); ?></p>

										<div class="mvbs-product-price-section">
											<span class="mvbs-product-price">$<?php echo number_format(floatval($price), 2); ?></span>
											<a href="<?php echo get_permalink($product->ID); ?>" class="mvbs-btn mvbs-btn-primary mvbs-btn-small">
												<?php _e('View Details', 'multi-vender-book-store'); ?>
											</a>
										</div>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>

			<div class="mvbs-contact-vendor">
				<h3><?php _e('Contact Vendor', 'multi-vender-book-store'); ?></h3>
				<p><?php _e('Interested in a book? Contact the vendor directly:', 'multi-vender-book-store'); ?></p>
				<div class="mvbs-contact-actions">
					<a href="mailto:<?php echo esc_attr($vendor->business_email); ?>" class="mvbs-btn mvbs-btn-primary">
						<span class="mvbs-btn-icon">✉️</span>
						<?php _e('Send Email', 'multi-vender-book-store'); ?>
					</a>
					<?php if ($vendor->business_phone): ?>
						<a href="tel:<?php echo esc_attr($vendor->business_phone); ?>" class="mvbs-btn mvbs-btn-secondary">
							<span class="mvbs-btn-icon">📞</span>
							<?php _e('Call', 'multi-vender-book-store'); ?>
						</a>
					<?php endif; ?>
				</div>
			</div>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Messaging shortcode.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Messaging HTML.
	 */
	public function messaging_shortcode($atts) {
		if (!is_user_logged_in()) {
			return '<p>' . __('Please log in to access messaging.', 'multi-vender-book-store') . '</p>';
		}

		$current_user_id = get_current_user_id();
		$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'inbox';

		// Handle form submissions
		if (isset($_POST['mvbs_send_message'])) {
			$result = Multi_Vender_Book_Store_Message::send_message($_POST);
			if ($result['success']) {
				$success_message = '<div class="mvbs-success">' . esc_html($result['message']) . '</div>';
			} else {
				$error_message = '<div class="mvbs-error">' . esc_html($result['message']) . '</div>';
			}
		}

		switch ($action) {
			case 'compose':
				return $this->render_compose_message($atts);
			case 'conversation':
				return $this->render_conversation($atts);
			default:
				return $this->render_message_inbox($current_user_id);
		}
	}

	/**
	 * Render message inbox.
	 *
	 * @since    1.0.0
	 * @param    int  $user_id  User ID.
	 * @return   string         Inbox HTML.
	 */
	private function render_message_inbox($user_id) {
		$messages = Multi_Vender_Book_Store_Message::get_user_messages($user_id, array(
			'type' => 'received',
			'limit' => 20
		));

		$unread_count = Multi_Vender_Book_Store_Message::get_unread_count($user_id);

		ob_start();
		?>
		<div class="mvbs-messaging">
			<div class="mvbs-messaging-header">
				<h2><?php _e('Messages', 'multi-vender-book-store'); ?></h2>
				<?php if ($unread_count > 0): ?>
					<span class="mvbs-unread-badge"><?php echo intval($unread_count); ?> <?php _e('unread', 'multi-vender-book-store'); ?></span>
				<?php endif; ?>
				<div class="mvbs-messaging-actions">
					<a href="?action=compose" class="mvbs-btn mvbs-btn-primary">
						<span class="mvbs-btn-icon">✉️</span>
						<?php _e('Compose Message', 'multi-vender-book-store'); ?>
					</a>
				</div>
			</div>

			<?php if (empty($messages)): ?>
				<div class="mvbs-empty-state">
					<div class="mvbs-empty-icon">💬</div>
					<h3><?php _e('No Messages', 'multi-vender-book-store'); ?></h3>
					<p><?php _e('You haven\'t received any messages yet.', 'multi-vender-book-store'); ?></p>
					<a href="?action=compose" class="mvbs-btn mvbs-btn-primary"><?php _e('Send Your First Message', 'multi-vender-book-store'); ?></a>
				</div>
			<?php else: ?>
				<div class="mvbs-messages-list">
					<?php foreach ($messages as $message): ?>
						<div class="mvbs-message-item <?php echo $message->is_read ? '' : 'mvbs-unread'; ?>">
							<div class="mvbs-message-avatar">
								<span class="mvbs-avatar-icon">👤</span>
							</div>
							<div class="mvbs-message-content">
								<div class="mvbs-message-header">
									<h4><?php echo esc_html($message->sender_name); ?></h4>
									<span class="mvbs-message-time"><?php echo human_time_diff(strtotime($message->created_at), current_time('timestamp')) . ' ' . __('ago', 'multi-vender-book-store'); ?></span>
								</div>
								<h5 class="mvbs-message-subject"><?php echo esc_html($message->subject ?: __('No Subject', 'multi-vender-book-store')); ?></h5>
								<p class="mvbs-message-preview"><?php echo esc_html(wp_trim_words($message->message, 15)); ?></p>
								<?php if ($message->product_id): ?>
									<p class="mvbs-message-context"><?php _e('About:', 'multi-vender-book-store'); ?> <?php echo esc_html($message->product_title); ?></p>
								<?php endif; ?>
							</div>
							<div class="mvbs-message-actions">
								<a href="?action=conversation&with=<?php echo $message->sender_id; ?>&product_id=<?php echo $message->product_id; ?>" class="mvbs-btn mvbs-btn-small mvbs-btn-secondary">
									<?php _e('Reply', 'multi-vender-book-store'); ?>
								</a>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Render compose message form.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Compose form HTML.
	 */
	private function render_compose_message($atts) {
		$recipient_id = isset($_GET['to']) ? intval($_GET['to']) : 0;
		$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

		ob_start();
		?>
		<div class="mvbs-compose-message">
			<div class="mvbs-page-header">
				<h2><?php _e('Compose Message', 'multi-vender-book-store'); ?></h2>
				<a href="?" class="mvbs-btn mvbs-btn-outline">← <?php _e('Back to Messages', 'multi-vender-book-store'); ?></a>
			</div>

			<form method="post" action="" class="mvbs-message-form">
				<?php wp_nonce_field('mvbs_send_message', 'mvbs_nonce'); ?>
				<input type="hidden" name="sender_id" value="<?php echo get_current_user_id(); ?>" />

				<div class="mvbs-form-group">
					<label for="recipient_id"><?php _e('To', 'multi-vender-book-store'); ?> <span class="mvbs-required">*</span></label>
					<select name="recipient_id" id="recipient_id" required>
						<option value=""><?php _e('Select recipient', 'multi-vender-book-store'); ?></option>
						<?php
						// Get vendors for dropdown
						$vendors = Multi_Vender_Book_Store_Database::get_vendors(array('status' => 'approved'));
						foreach ($vendors as $vendor) {
							$user = get_user_by('ID', $vendor->user_id);
							if ($user) {
								echo '<option value="' . $vendor->user_id . '" ' . selected($recipient_id, $vendor->user_id, false) . '>' . esc_html($vendor->business_name) . '</option>';
							}
						}
						?>
					</select>
				</div>

				<?php if ($product_id): ?>
					<input type="hidden" name="product_id" value="<?php echo $product_id; ?>" />
					<div class="mvbs-form-group">
						<label><?php _e('Regarding Product', 'multi-vender-book-store'); ?></label>
						<p class="mvbs-product-context"><?php echo esc_html(get_the_title($product_id)); ?></p>
					</div>
				<?php endif; ?>

				<div class="mvbs-form-group">
					<label for="subject"><?php _e('Subject', 'multi-vender-book-store'); ?></label>
					<input type="text" name="subject" id="subject" value="<?php echo esc_attr($_POST['subject'] ?? ''); ?>" placeholder="<?php _e('Enter message subject', 'multi-vender-book-store'); ?>" />
				</div>

				<div class="mvbs-form-group">
					<label for="message"><?php _e('Message', 'multi-vender-book-store'); ?> <span class="mvbs-required">*</span></label>
					<textarea name="message" id="message" required rows="6" placeholder="<?php _e('Type your message here...', 'multi-vender-book-store'); ?>"><?php echo esc_textarea($_POST['message'] ?? ''); ?></textarea>
				</div>

				<div class="mvbs-form-actions">
					<button type="submit" name="mvbs_send_message" class="mvbs-btn mvbs-btn-primary mvbs-btn-large">
						<span class="mvbs-btn-icon">📤</span>
						<?php _e('Send Message', 'multi-vender-book-store'); ?>
					</button>
					<a href="?" class="mvbs-btn mvbs-btn-secondary mvbs-btn-large"><?php _e('Cancel', 'multi-vender-book-store'); ?></a>
				</div>
			</form>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Render conversation view.
	 *
	 * @since    1.0.0
	 * @param    array  $atts  Shortcode attributes.
	 * @return   string        Conversation HTML.
	 */
	private function render_conversation($atts) {
		$with_user_id = isset($_GET['with']) ? intval($_GET['with']) : 0;
		$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
		$current_user_id = get_current_user_id();

		if (!$with_user_id) {
			return '<p>' . __('Invalid conversation.', 'multi-vender-book-store') . '</p>';
		}

		$messages = Multi_Vender_Book_Store_Message::get_conversation($current_user_id, $with_user_id, $product_id);
		$other_user = get_user_by('ID', $with_user_id);

		ob_start();
		?>
		<div class="mvbs-conversation">
			<div class="mvbs-conversation-header">
				<h2><?php printf(__('Conversation with %s', 'multi-vender-book-store'), esc_html($other_user->display_name)); ?></h2>
				<a href="?" class="mvbs-btn mvbs-btn-outline">← <?php _e('Back to Messages', 'multi-vender-book-store'); ?></a>
			</div>

			<div class="mvbs-conversation-messages">
				<?php if (empty($messages)): ?>
					<p class="mvbs-no-messages"><?php _e('No messages in this conversation yet.', 'multi-vender-book-store'); ?></p>
				<?php else: ?>
					<?php foreach ($messages as $message): ?>
						<div class="mvbs-conversation-message <?php echo $message->sender_id == $current_user_id ? 'mvbs-sent' : 'mvbs-received'; ?>">
							<div class="mvbs-message-bubble">
								<?php if ($message->subject): ?>
									<h5 class="mvbs-message-subject"><?php echo esc_html($message->subject); ?></h5>
								<?php endif; ?>
								<p><?php echo nl2br(esc_html($message->message)); ?></p>
								<span class="mvbs-message-time"><?php echo date('M j, Y g:i A', strtotime($message->created_at)); ?></span>
							</div>
						</div>
					<?php endforeach; ?>
				<?php endif; ?>
			</div>

			<div class="mvbs-reply-form">
				<h3><?php _e('Reply', 'multi-vender-book-store'); ?></h3>
				<form method="post" action="">
					<?php wp_nonce_field('mvbs_send_message', 'mvbs_nonce'); ?>
					<input type="hidden" name="sender_id" value="<?php echo $current_user_id; ?>" />
					<input type="hidden" name="recipient_id" value="<?php echo $with_user_id; ?>" />
					<?php if ($product_id): ?>
						<input type="hidden" name="product_id" value="<?php echo $product_id; ?>" />
					<?php endif; ?>

					<div class="mvbs-form-group">
						<textarea name="message" required rows="4" placeholder="<?php _e('Type your reply...', 'multi-vender-book-store'); ?>"></textarea>
					</div>

					<div class="mvbs-form-actions">
						<button type="submit" name="mvbs_send_message" class="mvbs-btn mvbs-btn-primary">
							<span class="mvbs-btn-icon">📤</span>
							<?php _e('Send Reply', 'multi-vender-book-store'); ?>
						</button>
					</div>
				</form>
			</div>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Add contact vendor button to WooCommerce product pages.
	 *
	 * @since    1.0.0
	 */
	public function add_contact_vendor_button() {
		global $product;

		if (!$product) {
			return;
		}

		// Check if this is a vendor product
		$vendor = Multi_Vender_Book_Store_Order::get_product_vendor($product->get_id());

		if (!$vendor) {
			return;
		}

		$messaging_page_id = get_option('mvbs_messaging_page_id');
		$vendor_profile_page_id = get_option('mvbs_vendor_profile_page_id');

		if (!$messaging_page_id || !$vendor_profile_page_id) {
			return;
		}

		$contact_url = add_query_arg(array(
			'action' => 'compose',
			'to' => $vendor->user_id,
			'product_id' => $product->get_id()
		), get_permalink($messaging_page_id));

		$vendor_profile_url = add_query_arg(array(
			'vendor_id' => $vendor->id
		), get_permalink($vendor_profile_page_id));

		?>
		<div class="mvbs-vendor-info">
			<h4><?php _e('Sold by', 'multi-vender-book-store'); ?></h4>
			<div class="mvbs-vendor-details">
				<p class="mvbs-vendor-name">
					<a href="<?php echo esc_url($vendor_profile_url); ?>"><?php echo esc_html($vendor->business_name); ?></a>
				</p>
				<div class="mvbs-vendor-actions">
					<?php if (is_user_logged_in()): ?>
						<a href="<?php echo esc_url($contact_url); ?>" class="mvbs-btn mvbs-btn-secondary mvbs-btn-small">
							<span class="mvbs-btn-icon">💬</span>
							<?php _e('Contact Vendor', 'multi-vender-book-store'); ?>
						</a>
					<?php endif; ?>
					<a href="<?php echo esc_url($vendor_profile_url); ?>" class="mvbs-btn mvbs-btn-outline mvbs-btn-small">
						<span class="mvbs-btn-icon">👤</span>
						<?php _e('View Profile', 'multi-vender-book-store'); ?>
					</a>
				</div>
			</div>
		</div>
		<?php
	}

}