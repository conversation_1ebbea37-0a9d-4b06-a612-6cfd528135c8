/**
 * All of the CSS for your public-facing functionality should be
 * included in this file.
 */

/* Vendor Registration & Login Forms */
.mvbs-vendor-registration,
.mvbs-vendor-login {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

.mvbs-vendor-registration h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 5px;
}

.mvbs-vendor-registration p,
.mvbs-vendor-login p {
    margin-bottom: 15px;
}

.mvbs-vendor-registration label,
.mvbs-vendor-login label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.mvbs-vendor-registration input[type="text"],
.mvbs-vendor-registration input[type="email"],
.mvbs-vendor-registration input[type="tel"],
.mvbs-vendor-registration input[type="password"],
.mvbs-vendor-registration textarea,
.mvbs-vendor-login input[type="text"],
.mvbs-vendor-login input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.mvbs-vendor-registration textarea {
    resize: vertical;
    min-height: 80px;
}

/* Vendor Dashboard */
.mvbs-vendor-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.mvbs-vendor-dashboard h2 {
    margin-bottom: 30px;
    color: #333;
}

.mvbs-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mvbs-stat-box {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.mvbs-stat-box h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
}

.mvbs-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    margin: 0;
}

.mvbs-dashboard-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.mvbs-dashboard-actions .button {
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
}

/* Messages */
.mvbs-success {
    background: #d4edda;
    color: #155724;
    padding: 12px 15px;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    margin-bottom: 20px;
}

.mvbs-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mvbs-vendor-registration,
    .mvbs-vendor-login {
        margin: 10px;
        padding: 15px;
    }

    .mvbs-dashboard-stats {
        grid-template-columns: 1fr;
    }

    .mvbs-dashboard-actions {
        flex-direction: column;
    }

    .mvbs-dashboard-actions .button {
        text-align: center;
    }
}