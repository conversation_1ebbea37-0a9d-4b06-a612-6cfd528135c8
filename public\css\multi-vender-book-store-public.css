/**
 * Multi-Vendor Book Store - Modern Frontend UI System
 * Consistent with admin interface design
 */

/* Global Variables and Base Styles */
:root {
    --mvbs-primary: #667eea;
    --mvbs-primary-dark: #764ba2;
    --mvbs-secondary: #6b7280;
    --mvbs-success: #10b981;
    --mvbs-warning: #f59e0b;
    --mvbs-error: #ef4444;
    --mvbs-white: #ffffff;
    --mvbs-gray-50: #f8fafc;
    --mvbs-gray-100: #f1f5f9;
    --mvbs-gray-200: #e2e8f0;
    --mvbs-gray-300: #cbd5e1;
    --mvbs-gray-400: #94a3b8;
    --mvbs-gray-500: #64748b;
    --mvbs-gray-600: #475569;
    --mvbs-gray-700: #334155;
    --mvbs-gray-800: #1e293b;
    --mvbs-gray-900: #0f172a;
    --mvbs-border-radius: 12px;
    --mvbs-border-radius-lg: 16px;
    --mvbs-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --mvbs-shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
    --mvbs-transition: all 0.3s ease;
}

/* Modern Registration & Login Forms */
.mvbs-vendor-registration,
.mvbs-vendor-login {
    max-width: 520px;
    margin: 40px auto;
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius-lg);
    box-shadow: var(--mvbs-shadow-lg);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mvbs-registration-header,
.mvbs-login-header {
    background: linear-gradient(135deg, var(--mvbs-primary) 0%, var(--mvbs-primary-dark) 100%);
    color: var(--mvbs-white);
    padding: 40px 32px;
    text-align: center;
    position: relative;
}

.mvbs-registration-header::before,
.mvbs-login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.mvbs-registration-header h2,
.mvbs-login-header h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.mvbs-registration-subtitle,
.mvbs-login-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
    position: relative;
    z-index: 1;
}

.mvbs-registration-form,
.mvbs-login-form {
    padding: 40px 32px;
}

.mvbs-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.mvbs-form-group {
    margin-bottom: 24px;
}

.mvbs-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.mvbs-required {
    color: #ef4444;
    font-weight: 700;
}

.mvbs-form-group input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #f1f5f9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafbfc;
    box-sizing: border-box;
}

.mvbs-form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.mvbs-form-group input::placeholder {
    color: #9ca3af;
    font-size: 14px;
}

.mvbs-form-help {
    margin-top: 6px;
    font-size: 12px;
    color: #6b7280;
}

.mvbs-form-actions {
    margin: 32px 0 24px 0;
}

.mvbs-form-footer {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid #f1f5f9;
}

.mvbs-form-footer p {
    margin: 8px 0;
    color: #6b7280;
    font-size: 14px;
}

.mvbs-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.mvbs-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.mvbs-vendor-registration textarea {
    resize: vertical;
    min-height: 80px;
}

/* Modern Vendor Dashboard */
.mvbs-vendor-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Dashboard Header */
.mvbs-dashboard-header {
    background: linear-gradient(135deg, var(--mvbs-primary) 0%, var(--mvbs-primary-dark) 100%);
    color: var(--mvbs-white);
    padding: 40px;
    border-radius: var(--mvbs-border-radius-lg);
    margin-bottom: 30px;
    box-shadow: var(--mvbs-shadow-lg);
}

.mvbs-dashboard-header h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.mvbs-dashboard-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.mvbs-dashboard-welcome {
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius);
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-dashboard-welcome h3 {
    margin: 0 0 8px 0;
    color: var(--mvbs-gray-800);
    font-size: 20px;
}

.mvbs-dashboard-welcome p {
    margin: 0;
    color: var(--mvbs-gray-600);
}

/* Dashboard Stats */
.mvbs-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.mvbs-stat-box {
    background: var(--mvbs-white);
    padding: 24px;
    border-radius: var(--mvbs-border-radius);
    box-shadow: var(--mvbs-shadow);
    transition: var(--mvbs-transition);
    position: relative;
    overflow: hidden;
}

.mvbs-stat-box:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.mvbs-stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--mvbs-primary), var(--mvbs-primary-dark));
}

.mvbs-stat-box h3 {
    margin: 0 0 12px 0;
    color: var(--mvbs-gray-600);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--mvbs-gray-800);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mvbs-stat-icon {
    font-size: 24px;
}

/* Dashboard Actions */
.mvbs-dashboard-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 30px;
}

.mvbs-dashboard-actions .mvbs-btn {
    justify-content: center;
    padding: 16px 24px;
    font-size: 15px;
}

/* Dashboard Navigation */
.mvbs-dashboard-nav {
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-nav-tabs {
    display: flex;
    gap: 8px;
    border-bottom: 2px solid var(--mvbs-gray-100);
    padding-bottom: 16px;
    margin-bottom: 20px;
}

.mvbs-nav-tab {
    padding: 12px 20px;
    border-radius: var(--mvbs-border-radius);
    text-decoration: none;
    color: var(--mvbs-gray-600);
    font-weight: 600;
    transition: var(--mvbs-transition);
    border: 2px solid transparent;
}

.mvbs-nav-tab:hover {
    background: var(--mvbs-gray-50);
    color: var(--mvbs-gray-800);
}

.mvbs-nav-tab.active {
    background: var(--mvbs-primary);
    color: var(--mvbs-white);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Modern Messages & Alerts */
.mvbs-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    padding: 16px 20px;
    border: 2px solid #10b981;
    border-radius: var(--mvbs-border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-success::before {
    content: '✅';
    font-size: 18px;
}

.mvbs-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    padding: 16px 20px;
    border: 2px solid #ef4444;
    border-radius: var(--mvbs-border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-error::before {
    content: '❌';
    font-size: 18px;
}

.mvbs-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    padding: 16px 20px;
    border: 2px solid #f59e0b;
    border-radius: var(--mvbs-border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-warning::before {
    content: '⚠️';
    font-size: 18px;
}

.mvbs-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    padding: 16px 20px;
    border: 2px solid #3b82f6;
    border-radius: var(--mvbs-border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-info::before {
    content: 'ℹ️';
    font-size: 18px;
}

/* Modern Button System - Consistent with Admin */
.mvbs-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--mvbs-border-radius);
    font-weight: 600;
    font-size: 14px;
    font-family: inherit;
    cursor: pointer;
    transition: var(--mvbs-transition);
    text-decoration: none;
    box-sizing: border-box;
    line-height: 1.4;
}

.mvbs-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.mvbs-btn-primary {
    background: linear-gradient(135deg, var(--mvbs-primary) 0%, var(--mvbs-primary-dark) 100%);
    color: var(--mvbs-white);
    box-shadow: var(--mvbs-shadow);
}

.mvbs-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: var(--mvbs-white);
}

.mvbs-btn-secondary {
    background: var(--mvbs-white);
    color: var(--mvbs-gray-700);
    border: 2px solid var(--mvbs-gray-200);
}

.mvbs-btn-secondary:hover {
    background: var(--mvbs-gray-50);
    border-color: var(--mvbs-primary);
    color: var(--mvbs-primary);
    transform: translateY(-1px);
}

.mvbs-btn-outline {
    background: transparent;
    color: var(--mvbs-primary);
    border: 2px solid var(--mvbs-primary);
}

.mvbs-btn-outline:hover {
    background: var(--mvbs-primary);
    color: var(--mvbs-white);
    transform: translateY(-1px);
}

.mvbs-btn-success {
    background: var(--mvbs-success);
    color: var(--mvbs-white);
}

.mvbs-btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
    color: var(--mvbs-white);
}

.mvbs-btn-warning {
    background: var(--mvbs-warning);
    color: var(--mvbs-white);
}

.mvbs-btn-warning:hover {
    background: #d97706;
    transform: translateY(-1px);
    color: var(--mvbs-white);
}

.mvbs-btn-error {
    background: var(--mvbs-error);
    color: var(--mvbs-white);
}

.mvbs-btn-error:hover {
    background: #dc2626;
    transform: translateY(-1px);
    color: var(--mvbs-white);
}

.mvbs-btn-large {
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 700;
}

.mvbs-btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

.mvbs-btn-icon {
    font-size: 16px;
    line-height: 1;
}

.mvbs-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 14px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    min-width: 140px;
}

.mvbs-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.mvbs-btn:hover::before {
    left: 100%;
}

.mvbs-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.mvbs-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.mvbs-btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    border: 2px solid #e2e8f0;
}

.mvbs-btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-2px);
    color: #334155;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.mvbs-btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.mvbs-btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.mvbs-btn-small {
    padding: 10px 20px;
    font-size: 13px;
    min-width: 100px;
}

.mvbs-btn-large {
    padding: 18px 36px;
    font-size: 17px;
    min-width: 180px;
}

.mvbs-btn-icon {
    font-size: 18px;
}

/* Enhanced Dashboard */
.mvbs-dashboard-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.mvbs-dashboard-header h2 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
}

.mvbs-dashboard-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.mvbs-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.mvbs-stat-card {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.3s ease;
}

.mvbs-stat-card:hover {
    transform: translateY(-4px);
}

.mvbs-stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.mvbs-stat-primary .mvbs-stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.mvbs-stat-success .mvbs-stat-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.mvbs-stat-info .mvbs-stat-icon {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.mvbs-stat-warning .mvbs-stat-icon {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.mvbs-stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-stat-number {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.mvbs-dashboard-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 40px;
}

.mvbs-recent-activity {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-recent-activity h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.mvbs-no-activity {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

/* Pending Approval State */
.mvbs-pending-approval {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-pending-approval .mvbs-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.mvbs-pending-approval h3 {
    color: #f39c12;
    margin-bottom: 16px;
}

/* Page Headers */
.mvbs-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f8f9fa;
}

.mvbs-page-header h2 {
    margin: 0;
    color: #2c3e50;
}

.mvbs-page-subtitle {
    margin: 4px 0 0 0;
    color: #6c757d;
}

.mvbs-page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.mvbs-back-btn {
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mvbs-vendor-registration,
    .mvbs-vendor-login {
        margin: 10px;
        padding: 15px;
    }

    .mvbs-dashboard-stats {
        grid-template-columns: 1fr;
    }

    .mvbs-dashboard-actions {
        grid-template-columns: 1fr;
    }

    .mvbs-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .mvbs-page-actions {
        width: 100%;
        justify-content: space-between;
    }

    .mvbs-dashboard-header {
        padding: 24px 16px;
    }

    .mvbs-dashboard-header h2 {
        font-size: 24px;
    }
}

/* Product Forms */
.mvbs-product-form {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    max-width: 800px;
    margin: 0 auto;
}

.mvbs-form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f3f4;
}

.mvbs-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.mvbs-form-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.mvbs-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.mvbs-form-group {
    display: flex;
    flex-direction: column;
}

.mvbs-form-group-full {
    grid-column: 1 / -1;
}

.mvbs-form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.mvbs-form-group input,
.mvbs-form-group select,
.mvbs-form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: #fafafa;
}

.mvbs-form-group input:focus,
.mvbs-form-group select:focus,
.mvbs-form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.mvbs-form-group input[type="file"] {
    padding: 8px;
    background: white;
}

.mvbs-form-help {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.mvbs-form-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    padding-top: 24px;
    border-top: 1px solid #f1f3f4;
}

/* Product Management */
.mvbs-empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.mvbs-empty-state h3 {
    color: #374151;
    margin-bottom: 12px;
}

.mvbs-empty-state p {
    color: #6b7280;
    margin-bottom: 24px;
}

.mvbs-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.mvbs-product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mvbs-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-product-image {
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mvbs-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mvbs-no-image {
    font-size: 48px;
    color: #dee2e6;
}

.mvbs-product-info {
    padding: 20px;
}

.mvbs-product-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.mvbs-product-price {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 700;
    color: #27ae60;
}

.mvbs-product-condition {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-product-status {
    margin-bottom: 16px;
}

.mvbs-status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-status-approved {
    background: #d4edda;
    color: #155724;
}

.mvbs-status-pending {
    background: #fff3cd;
    color: #856404;
}

.mvbs-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.mvbs-status-draft {
    background: #e2e3e5;
    color: #383d41;
}

.mvbs-product-actions {
    display: flex;
    gap: 8px;
}

/* Success States */
.mvbs-success {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.mvbs-success-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.mvbs-success h3 {
    color: #27ae60;
    margin-bottom: 12px;
}

.mvbs-success p {
    color: #6b7280;
    margin-bottom: 24px;
}

/* Admin Viewing Notice */
.mvbs-admin-viewing-notice {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 14px rgba(251, 191, 36, 0.3);
}

.mvbs-admin-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.mvbs-admin-viewing-notice p {
    margin: 0;
    font-weight: 500;
}

/* Mobile Responsive for Forms */
@media (max-width: 768px) {
    .mvbs-vendor-registration,
    .mvbs-vendor-login {
        margin: 20px 10px;
        border-radius: 16px;
    }

    .mvbs-registration-header,
    .mvbs-login-header {
        padding: 24px 20px;
    }

    .mvbs-registration-form,
    .mvbs-login-form {
        padding: 24px 20px;
    }

    .mvbs-form-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-btn-large {
        padding: 16px 24px;
        font-size: 16px;
        width: 100%;
    }

    .mvbs-product-form {
        padding: 20px;
        margin: 10px;
    }

    .mvbs-form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .mvbs-form-actions {
        flex-direction: column;
    }

    .mvbs-products-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .mvbs-page-actions {
        width: 100%;
        flex-direction: column;
        gap: 8px;
    }

    .mvbs-admin-viewing-notice {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
}

/* Orders Management */
.mvbs-orders-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.mvbs-order-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 24px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mvbs-order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.mvbs-order-info h4 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.mvbs-order-date {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

.mvbs-order-amount {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.mvbs-amount {
    font-size: 20px;
    font-weight: 700;
    color: #27ae60;
}

.mvbs-order-details {
    margin-bottom: 20px;
}

.mvbs-order-details p {
    margin: 8px 0;
    color: #374151;
    font-size: 14px;
}

.mvbs-order-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Enhanced Status Badges */
.mvbs-status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-status-pending {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
}

.mvbs-status-paid {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.mvbs-status-cancelled {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.mvbs-status-processing {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

/* Public Vendor Profile */
.mvbs-public-vendor-profile {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.mvbs-vendor-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 40px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 24px;
    align-items: center;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.mvbs-vendor-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

.mvbs-vendor-info h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
}

.mvbs-vendor-email,
.mvbs-vendor-phone,
.mvbs-vendor-address {
    margin: 4px 0;
    opacity: 0.9;
    font-size: 16px;
}

.mvbs-vendor-stats {
    display: flex;
    gap: 24px;
}

.mvbs-stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 16px 20px;
    border-radius: 12px;
    min-width: 80px;
}

.mvbs-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.mvbs-stat-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-vendor-products-section {
    margin-bottom: 40px;
}

.mvbs-vendor-products-section h2 {
    margin-bottom: 24px;
    color: #2c3e50;
    font-size: 24px;
}

.mvbs-public-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}

.mvbs-public-product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mvbs-public-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-public-product-card .mvbs-product-image {
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mvbs-public-product-card .mvbs-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mvbs-public-product-card:hover .mvbs-product-image img {
    transform: scale(1.05);
}

.mvbs-public-product-card .mvbs-no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 48px;
    color: #dee2e6;
    text-decoration: none;
}

.mvbs-product-details {
    padding: 20px;
}

.mvbs-product-details h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    line-height: 1.4;
}

.mvbs-product-details h3 a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.mvbs-product-details h3 a:hover {
    color: #667eea;
}

.mvbs-product-author {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-style: italic;
    font-size: 14px;
}

.mvbs-product-condition {
    margin: 0 0 16px 0;
    color: #6b7280;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-product-price-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mvbs-product-price {
    font-size: 20px;
    font-weight: 700;
    color: #27ae60;
}

.mvbs-contact-vendor {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
}

.mvbs-contact-vendor h3 {
    margin: 0 0 12px 0;
    color: #2c3e50;
}

.mvbs-contact-vendor p {
    margin: 0 0 24px 0;
    color: #6b7280;
}

.mvbs-contact-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* Mobile Responsive for Public Profile */
@media (max-width: 768px) {
    .mvbs-vendor-header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 16px;
    }

    .mvbs-vendor-stats {
        justify-content: center;
    }

    .mvbs-public-products-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-contact-actions {
        flex-direction: column;
    }

    .mvbs-order-header {
        flex-direction: column;
        gap: 12px;
    }

    .mvbs-order-amount {
        align-items: flex-start;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .mvbs-order-actions {
        justify-content: flex-start;
    }
}

/* Messaging System */
.mvbs-messaging {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.mvbs-messaging-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.mvbs-messaging-header h2 {
    margin: 0;
    color: #2c3e50;
}

.mvbs-unread-badge {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 12px;
}

.mvbs-messaging-actions {
    display: flex;
    gap: 12px;
}

.mvbs-messages-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.mvbs-message-item {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 20px;
    display: flex;
    gap: 16px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid transparent;
}

.mvbs-message-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Modern Vendor Profile Styles */
.mvbs-public-vendor-profile {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mvbs-vendor-header {
    background: linear-gradient(135deg, var(--mvbs-primary) 0%, var(--mvbs-primary-dark) 100%);
    color: var(--mvbs-white);
    padding: 40px;
    border-radius: var(--mvbs-border-radius-lg);
    margin-bottom: 30px;
    box-shadow: var(--mvbs-shadow-lg);
    display: flex;
    align-items: center;
    gap: 24px;
}

.mvbs-vendor-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    flex-shrink: 0;
}

.mvbs-vendor-info h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
}

.mvbs-vendor-email,
.mvbs-vendor-phone,
.mvbs-vendor-address {
    margin: 4px 0;
    opacity: 0.9;
    font-size: 14px;
}

.mvbs-vendor-stats {
    display: flex;
    gap: 24px;
    margin-left: auto;
}

.mvbs-stat-item {
    text-align: center;
}

.mvbs-stat-item .mvbs-stat-number {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: var(--mvbs-white);
}

.mvbs-stat-item .mvbs-stat-label {
    font-size: 12px;
    opacity: 0.8;
    margin: 4px 0 0 0;
}

/* Modern Product Cards */
.mvbs-public-products-grid,
.mvbs-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 30px;
}

.mvbs-public-product-card,
.mvbs-product-card {
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius);
    box-shadow: var(--mvbs-shadow);
    overflow: hidden;
    transition: var(--mvbs-transition);
    position: relative;
}

.mvbs-public-product-card:hover,
.mvbs-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.mvbs-product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: var(--mvbs-gray-100);
}

.mvbs-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--mvbs-transition);
}

.mvbs-product-image:hover img {
    transform: scale(1.05);
}

.mvbs-no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 48px;
    color: var(--mvbs-gray-400);
    text-decoration: none;
}

.mvbs-product-content {
    padding: 20px;
}

.mvbs-product-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--mvbs-gray-800);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.mvbs-product-title a {
    color: inherit;
    text-decoration: none;
}

.mvbs-product-title a:hover {
    color: var(--mvbs-primary);
}

.mvbs-product-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--mvbs-success);
    margin: 8px 0;
}

.mvbs-product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--mvbs-gray-100);
    font-size: 12px;
    color: var(--mvbs-gray-500);
}

.mvbs-product-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.mvbs-status-approved {
    background: #d1fae5;
    color: #065f46;
}

.mvbs-status-pending {
    background: #fef3c7;
    color: #92400e;
}

.mvbs-status-rejected {
    background: #fee2e2;
    color: #991b1b;
}

/* Modern Empty States */
.mvbs-empty-state {
    text-align: center;
    padding: 80px 40px;
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius);
    box-shadow: var(--mvbs-shadow);
    margin: 30px 0;
}

.mvbs-empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.mvbs-empty-state h3 {
    font-size: 24px;
    color: var(--mvbs-gray-700);
    margin: 0 0 12px 0;
    font-weight: 600;
}

.mvbs-empty-state p {
    color: var(--mvbs-gray-500);
    font-size: 16px;
    max-width: 400px;
    margin: 0 auto 24px auto;
    line-height: 1.6;
}

/* Pending Approval State */
.mvbs-pending-approval {
    text-align: center;
    padding: 60px 40px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid var(--mvbs-warning);
    border-radius: var(--mvbs-border-radius-lg);
    margin: 30px 0;
    box-shadow: var(--mvbs-shadow);
}

.mvbs-pending-approval .mvbs-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.mvbs-pending-approval h3 {
    color: #92400e;
    font-size: 20px;
    margin: 0 0 12px 0;
}

.mvbs-pending-approval p {
    color: #92400e;
    margin: 0;
    line-height: 1.6;
}

/* Vendor Info Widget */
.mvbs-vendor-info {
    background: var(--mvbs-white);
    border-radius: var(--mvbs-border-radius);
    padding: 20px;
    box-shadow: var(--mvbs-shadow);
    margin: 20px 0;
}

.mvbs-vendor-info h4 {
    margin: 0 0 12px 0;
    color: var(--mvbs-gray-700);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-vendor-name a {
    color: var(--mvbs-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
}

.mvbs-vendor-name a:hover {
    text-decoration: underline;
}

.mvbs-vendor-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mvbs-vendor-registration,
    .mvbs-vendor-login {
        margin: 20px;
        max-width: none;
    }

    .mvbs-registration-header,
    .mvbs-login-header {
        padding: 30px 20px;
    }

    .mvbs-registration-form,
    .mvbs-login-form {
        padding: 30px 20px;
    }

    .mvbs-form-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-vendor-dashboard {
        padding: 15px;
    }

    .mvbs-dashboard-header {
        padding: 30px 20px;
    }

    .mvbs-dashboard-stats {
        grid-template-columns: 1fr;
    }

    .mvbs-dashboard-actions {
        grid-template-columns: 1fr;
    }

    .mvbs-vendor-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .mvbs-vendor-stats {
        margin-left: 0;
        justify-content: center;
    }

    .mvbs-public-products-grid,
    .mvbs-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }

    .mvbs-nav-tabs {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .mvbs-public-products-grid,
    .mvbs-products-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-vendor-stats {
        flex-direction: column;
        gap: 12px;
    }

    .mvbs-btn {
        width: 100%;
        justify-content: center;
    }

    .mvbs-vendor-actions {
        flex-direction: column;
    }
}
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-message-item.mvbs-unread {
    border-left-color: #667eea;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.mvbs-message-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.mvbs-message-content {
    flex: 1;
}

.mvbs-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.mvbs-message-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.mvbs-message-time {
    color: #6b7280;
    font-size: 12px;
}

.mvbs-message-subject {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 14px;
    font-weight: 600;
}

.mvbs-message-preview {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
}

.mvbs-message-context {
    margin: 0;
    color: #667eea;
    font-size: 12px;
    font-style: italic;
}

.mvbs-message-actions {
    display: flex;
    align-items: center;
}

/* Compose Message Form */
.mvbs-compose-message,
.mvbs-conversation {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.mvbs-message-form {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-product-context {
    background: #f8fafc;
    padding: 12px 16px;
    border-radius: 8px;
    color: #374151;
    font-weight: 500;
    margin: 0;
}

/* Conversation View */
.mvbs-conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.mvbs-conversation-messages {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    max-height: 500px;
    overflow-y: auto;
}

.mvbs-conversation-message {
    margin-bottom: 20px;
    display: flex;
}

.mvbs-conversation-message.mvbs-sent {
    justify-content: flex-end;
}

.mvbs-conversation-message.mvbs-received {
    justify-content: flex-start;
}

.mvbs-message-bubble {
    max-width: 70%;
    padding: 16px 20px;
    border-radius: 20px;
    position: relative;
}

.mvbs-sent .mvbs-message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 8px;
}

.mvbs-received .mvbs-message-bubble {
    background: #f8fafc;
    color: #374151;
    border-bottom-left-radius: 8px;
}

.mvbs-message-bubble .mvbs-message-subject {
    margin: 0 0 8px 0;
    font-weight: 600;
    font-size: 14px;
}

.mvbs-sent .mvbs-message-subject {
    color: rgba(255, 255, 255, 0.9);
}

.mvbs-message-bubble p {
    margin: 0 0 8px 0;
    line-height: 1.5;
}

.mvbs-message-bubble .mvbs-message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    text-align: right;
}

.mvbs-no-messages {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 40px;
}

.mvbs-reply-form {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-reply-form h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
}

/* Mobile Responsive for Messaging */
@media (max-width: 768px) {
    .mvbs-messaging-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .mvbs-message-item {
        flex-direction: column;
        gap: 12px;
    }

    .mvbs-message-avatar {
        align-self: flex-start;
    }

    .mvbs-conversation-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .mvbs-message-bubble {
        max-width: 90%;
    }

    .mvbs-compose-message,
    .mvbs-conversation {
        padding: 10px;
    }

    .mvbs-message-form,
    .mvbs-reply-form {
        padding: 20px;
    }
}

/* Vendor Info on Product Pages */
.mvbs-vendor-info {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.mvbs-vendor-info h4 {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-vendor-name {
    margin: 0 0 16px 0;
}

.mvbs-vendor-name a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: color 0.3s ease;
}

.mvbs-vendor-name a:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.mvbs-vendor-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
    .mvbs-vendor-actions {
        flex-direction: column;
    }

    .mvbs-vendor-actions .mvbs-btn {
        justify-content: center;
    }
}