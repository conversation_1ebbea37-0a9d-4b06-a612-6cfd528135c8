/**
 * All of the CSS for your public-facing functionality should be
 * included in this file.
 */

/* Vendor Registration & Login Forms */
.mvbs-vendor-registration,
.mvbs-vendor-login {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

.mvbs-vendor-registration h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 5px;
}

.mvbs-vendor-registration p,
.mvbs-vendor-login p {
    margin-bottom: 15px;
}

.mvbs-vendor-registration label,
.mvbs-vendor-login label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.mvbs-vendor-registration input[type="text"],
.mvbs-vendor-registration input[type="email"],
.mvbs-vendor-registration input[type="tel"],
.mvbs-vendor-registration input[type="password"],
.mvbs-vendor-registration textarea,
.mvbs-vendor-login input[type="text"],
.mvbs-vendor-login input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.mvbs-vendor-registration textarea {
    resize: vertical;
    min-height: 80px;
}

/* Vendor Dashboard */
.mvbs-vendor-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.mvbs-vendor-dashboard h2 {
    margin-bottom: 30px;
    color: #333;
}

.mvbs-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mvbs-stat-box {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.mvbs-stat-box h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
}

.mvbs-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    margin: 0;
}

.mvbs-dashboard-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.mvbs-dashboard-actions .button {
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
}

/* Messages */
.mvbs-success {
    background: #d4edda;
    color: #155724;
    padding: 12px 15px;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    margin-bottom: 20px;
}

.mvbs-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Modern Button System */
.mvbs-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mvbs-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.mvbs-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

.mvbs-btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.mvbs-btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    color: #495057;
}

.mvbs-btn-outline {
    background: transparent;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.mvbs-btn-outline:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.mvbs-btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

.mvbs-btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.mvbs-btn-icon {
    font-size: 16px;
}

/* Enhanced Dashboard */
.mvbs-dashboard-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.mvbs-dashboard-header h2 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
}

.mvbs-dashboard-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.mvbs-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.mvbs-stat-card {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.3s ease;
}

.mvbs-stat-card:hover {
    transform: translateY(-4px);
}

.mvbs-stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.mvbs-stat-primary .mvbs-stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.mvbs-stat-success .mvbs-stat-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.mvbs-stat-info .mvbs-stat-icon {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.mvbs-stat-warning .mvbs-stat-icon {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.mvbs-stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-stat-number {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.mvbs-dashboard-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 40px;
}

.mvbs-recent-activity {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-recent-activity h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.mvbs-no-activity {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

/* Pending Approval State */
.mvbs-pending-approval {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-pending-approval .mvbs-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.mvbs-pending-approval h3 {
    color: #f39c12;
    margin-bottom: 16px;
}

/* Page Headers */
.mvbs-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f8f9fa;
}

.mvbs-page-header h2 {
    margin: 0;
    color: #2c3e50;
}

.mvbs-page-subtitle {
    margin: 4px 0 0 0;
    color: #6c757d;
}

.mvbs-page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.mvbs-back-btn {
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mvbs-vendor-registration,
    .mvbs-vendor-login {
        margin: 10px;
        padding: 15px;
    }

    .mvbs-dashboard-stats {
        grid-template-columns: 1fr;
    }

    .mvbs-dashboard-actions {
        grid-template-columns: 1fr;
    }

    .mvbs-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .mvbs-page-actions {
        width: 100%;
        justify-content: space-between;
    }

    .mvbs-dashboard-header {
        padding: 24px 16px;
    }

    .mvbs-dashboard-header h2 {
        font-size: 24px;
    }
}

/* Product Forms */
.mvbs-product-form {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    max-width: 800px;
    margin: 0 auto;
}

.mvbs-form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f3f4;
}

.mvbs-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.mvbs-form-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.mvbs-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.mvbs-form-group {
    display: flex;
    flex-direction: column;
}

.mvbs-form-group-full {
    grid-column: 1 / -1;
}

.mvbs-form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.mvbs-form-group input,
.mvbs-form-group select,
.mvbs-form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: #fafafa;
}

.mvbs-form-group input:focus,
.mvbs-form-group select:focus,
.mvbs-form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.mvbs-form-group input[type="file"] {
    padding: 8px;
    background: white;
}

.mvbs-form-help {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.mvbs-form-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    padding-top: 24px;
    border-top: 1px solid #f1f3f4;
}

/* Product Management */
.mvbs-empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.mvbs-empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.mvbs-empty-state h3 {
    color: #374151;
    margin-bottom: 12px;
}

.mvbs-empty-state p {
    color: #6b7280;
    margin-bottom: 24px;
}

.mvbs-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.mvbs-product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mvbs-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.mvbs-product-image {
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mvbs-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mvbs-no-image {
    font-size: 48px;
    color: #dee2e6;
}

.mvbs-product-info {
    padding: 20px;
}

.mvbs-product-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.mvbs-product-price {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 700;
    color: #27ae60;
}

.mvbs-product-condition {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-product-status {
    margin-bottom: 16px;
}

.mvbs-status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mvbs-status-approved {
    background: #d4edda;
    color: #155724;
}

.mvbs-status-pending {
    background: #fff3cd;
    color: #856404;
}

.mvbs-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.mvbs-status-draft {
    background: #e2e3e5;
    color: #383d41;
}

.mvbs-product-actions {
    display: flex;
    gap: 8px;
}

/* Success States */
.mvbs-success {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.mvbs-success-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.mvbs-success h3 {
    color: #27ae60;
    margin-bottom: 12px;
}

.mvbs-success p {
    color: #6b7280;
    margin-bottom: 24px;
}

/* Mobile Responsive for Forms */
@media (max-width: 768px) {
    .mvbs-product-form {
        padding: 20px;
        margin: 10px;
    }

    .mvbs-form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .mvbs-form-actions {
        flex-direction: column;
    }

    .mvbs-products-grid {
        grid-template-columns: 1fr;
    }

    .mvbs-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .mvbs-page-actions {
        width: 100%;
        flex-direction: column;
        gap: 8px;
    }
}