(function( $ ) {
	'use strict';

	/**
	 * All of the code for your public-facing JavaScript source
	 * should reside in this file.
	 *
	 * Note: It has been assumed you will write jQuery code here, so the
	 * $ function reference has been prepared for usage within the scope
	 * of this function.
	 *
	 * This enables you to define handlers, for when the DOM is ready:
	 *
	 * $(function() {
	 *
	 * });
	 *
	 * When the window is loaded:
	 *
	 * $( window ).load(function() {
	 *
	 * });
	 *
	 * ...and/or other possibilities.
	 *
	 * Ideally, it is not considered best practise to attach more than a
	 * single DOM-ready or window-load handler for a particular page.
	 * Although scripts in the WordPress core, Plugins and Themes may be
	 * practising this, we should strive to set a better example in our own work.
	 */

	// Dashboard functionality
	$(document).ready(function() {
		// Initialize dashboard
		initializeDashboard();
	});

	function initializeDashboard() {
		// Handle sidebar toggle
		window.toggleSidebar = function() {
			const sidebar = document.getElementById('dashboardSidebar');
			const overlay = document.getElementById('mobileOverlay');
			const dashboard = document.querySelector('.mvbs-vendor-dashboard');

			if (window.innerWidth <= 1024) {
				// Mobile behavior
				if (sidebar) sidebar.classList.toggle('mobile-open');
				if (overlay) overlay.classList.toggle('active');
			} else {
				// Desktop behavior
				if (sidebar) sidebar.classList.toggle('collapsed');
				if (dashboard) dashboard.classList.toggle('sidebar-collapsed');
			}
		};

		// Close mobile sidebar when clicking overlay
		$(document).on('click', '#mobileOverlay', function() {
			const sidebar = document.getElementById('dashboardSidebar');
			const overlay = document.getElementById('mobileOverlay');

			if (sidebar) sidebar.classList.remove('mobile-open');
			if (overlay) overlay.classList.remove('active');
		});

		// Handle window resize
		$(window).on('resize', function() {
			const sidebar = document.getElementById('dashboardSidebar');
			const overlay = document.getElementById('mobileOverlay');

			if (window.innerWidth > 1024) {
				// Reset mobile classes on desktop
				if (sidebar) sidebar.classList.remove('mobile-open');
				if (overlay) overlay.classList.remove('active');
			}
		});
	}

})( jQuery );
